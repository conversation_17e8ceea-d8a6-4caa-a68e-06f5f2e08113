# 🍅 Pomodoro Timer

A modern, responsive Pomodoro timer application built with Vite and vanilla JavaScript.

## Features

- ⏱️ Classic Pomodoro technique (25 min work, 5 min break)
- 🔄 Automatic work/break cycle transitions
- ⚙️ Customizable timer durations
- 🎵 Sound notifications
- 🌙 Dark/Light theme support
- 📊 Session tracking and statistics
- 💾 Local storage for settings persistence
- 📱 Responsive design for all devices

## Project Structure

```
src/
├── components/     # Modular UI components
├── styles/         # CSS stylesheets
├── utils/          # Utility functions and classes
├── assets/         # Static assets
│   └── sounds/     # Audio notification files
├── main.js         # Application entry point
└── style.css       # Main stylesheet
```

## Development

```bash
# Install dependencies
npm install

# Start development server
npm run dev

# Build for production
npm run build

# Preview production build
npm run preview
```

## Technologies Used

- **Vite** - Fast build tool and dev server
- **Vanilla JavaScript** - Pure JS without frameworks
- **CSS3** - Modern styling with Grid/Flexbox
- **HTML5** - Semantic markup

## Getting Started

1. Clone the repository
2. Run `npm install` to install dependencies
3. Run `npm run dev` to start the development server
4. Open your browser to `http://localhost:5173`

## License

MIT License
