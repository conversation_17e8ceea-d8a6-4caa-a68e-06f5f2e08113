/**
 * BaseComponent Class - Base class for all UI components
 * Provides common functionality for component lifecycle and event handling
 */
export class BaseComponent {
  constructor(selector, app = null) {
    this.selector = selector;
    this.element = null;
    this.app = app;
    this.isInitialized = false;
    this.eventListeners = [];
    
    // Find or create element
    this.findElement();
  }

  /**
   * Find the DOM element for this component
   */
  findElement() {
    if (typeof this.selector === 'string') {
      this.element = document.querySelector(this.selector);
      if (!this.element) {
        console.warn(`Element not found for selector: ${this.selector}`);
      }
    } else if (this.selector instanceof HTMLElement) {
      this.element = this.selector;
    }
  }

  /**
   * Initialize the component
   */
  init() {
    if (this.isInitialized || !this.element) return;
    
    this.render();
    this.bindEvents();
    
    // Register with app if provided
    if (this.app && typeof this.app.registerComponent === 'function') {
      this.app.registerComponent(this.constructor.name, this);
    }
    
    this.isInitialized = true;
    this.onInit();
  }

  /**
   * Render the component (to be overridden by subclasses)
   */
  render() {
    // Override in subclasses
  }

  /**
   * Bind event listeners (to be overridden by subclasses)
   */
  bindEvents() {
    // Override in subclasses
  }

  /**
   * Handle application events (to be overridden by subclasses)
   */
  handleAppEvent(event, data) {
    // Override in subclasses
  }

  /**
   * Called after component initialization
   */
  onInit() {
    // Override in subclasses
  }

  /**
   * Add event listener and track it for cleanup
   */
  addEventListener(element, event, handler, options = {}) {
    const boundHandler = handler.bind(this);
    element.addEventListener(event, boundHandler, options);
    
    this.eventListeners.push({
      element,
      event,
      handler: boundHandler,
      options
    });
    
    return boundHandler;
  }

  /**
   * Create and append child element
   */
  createElement(tag, className = '', textContent = '', attributes = {}) {
    const element = document.createElement(tag);
    
    if (className) {
      element.className = className;
    }
    
    if (textContent) {
      element.textContent = textContent;
    }
    
    Object.keys(attributes).forEach(key => {
      element.setAttribute(key, attributes[key]);
    });
    
    return element;
  }

  /**
   * Update element content safely
   */
  updateContent(element, content) {
    if (element && element.textContent !== content) {
      element.textContent = content;
    }
  }

  /**
   * Toggle class on element
   */
  toggleClass(element, className, condition = null) {
    if (!element) return;
    
    if (condition !== null) {
      element.classList.toggle(className, condition);
    } else {
      element.classList.toggle(className);
    }
  }

  /**
   * Add class to element
   */
  addClass(element, className) {
    if (element && !element.classList.contains(className)) {
      element.classList.add(className);
    }
  }

  /**
   * Remove class from element
   */
  removeClass(element, className) {
    if (element && element.classList.contains(className)) {
      element.classList.remove(className);
    }
  }

  /**
   * Show element
   */
  show(element = this.element) {
    if (element) {
      element.style.display = '';
      element.removeAttribute('hidden');
    }
  }

  /**
   * Hide element
   */
  hide(element = this.element) {
    if (element) {
      element.style.display = 'none';
    }
  }

  /**
   * Get current state of the component
   */
  getState() {
    return {
      isInitialized: this.isInitialized,
      selector: this.selector,
      hasElement: !!this.element
    };
  }

  /**
   * Update component with new data
   */
  update(data) {
    // Override in subclasses
  }

  /**
   * Cleanup component resources
   */
  destroy() {
    // Remove all event listeners
    this.eventListeners.forEach(({ element, event, handler, options }) => {
      element.removeEventListener(event, handler, options);
    });
    this.eventListeners = [];
    
    // Unregister from app
    if (this.app && typeof this.app.unregisterComponent === 'function') {
      this.app.unregisterComponent(this.constructor.name);
    }
    
    this.isInitialized = false;
    this.onDestroy();
  }

  /**
   * Called before component destruction
   */
  onDestroy() {
    // Override in subclasses
  }

  /**
   * Emit custom event
   */
  emit(eventName, detail = {}) {
    if (this.element) {
      const customEvent = new CustomEvent(eventName, {
        detail,
        bubbles: true,
        cancelable: true
      });
      this.element.dispatchEvent(customEvent);
    }
  }

  /**
   * Listen for custom events
   */
  on(eventName, handler) {
    if (this.element) {
      this.addEventListener(this.element, eventName, handler);
    }
  }
}
