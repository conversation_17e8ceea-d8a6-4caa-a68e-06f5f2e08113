/**
 * BreakConfirmation Component - Shows break confirmation dialog
 */
import { BaseComponent } from './BaseComponent.js';

export class BreakConfirmation extends BaseComponent {
  constructor(selector, app) {
    super(selector, app);
    
    this.isVisible = false;
    this.dialog = null;
    this.overlay = null;
    this.updateInterval = null;
  }

  render() {
    if (!this.element) return;

    // Create overlay
    this.overlay = this.createElement('div', 'break-confirmation-overlay');
    document.body.appendChild(this.overlay);

    // Create dialog
    this.dialog = this.createElement('div', 'break-confirmation-dialog');
    this.dialog.innerHTML = `
      <div class="break-dialog-content">
        <div class="break-icon">
          <svg viewBox="0 0 24 24" width="48" height="48">
            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
          </svg>
        </div>
        
        <h2 class="break-title">Work Session Complete! 🎉</h2>
        
        <div class="break-info">
          <p class="break-message">Great job! Time for a <span class="break-type">break</span>.</p>
          <p class="break-subtitle">Take your time - any delay will be added as bonus break time!</p>
        </div>
        
        <div class="break-stats">
          <div class="stat-item">
            <span class="stat-label">Delay Time:</span>
            <span class="delay-time">00:00</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Bonus Time:</span>
            <span class="bonus-time">+00:00</span>
          </div>
        </div>
        
        <div class="break-actions">
          <button class="break-btn secondary" id="skip-break-btn">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill="currentColor"/>
            </svg>
            Skip Break
          </button>
          
          <button class="break-btn primary" id="start-break-btn">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M8 5v14l11-7z" fill="currentColor"/>
            </svg>
            Start Break
          </button>
        </div>
      </div>
    `;

    document.body.appendChild(this.dialog);
  }

  bindEvents() {
    if (!this.dialog || !this.overlay) return;

    // Button events
    const startBreakBtn = this.dialog.querySelector('#start-break-btn');
    const skipBreakBtn = this.dialog.querySelector('#skip-break-btn');

    this.addEventListener(startBreakBtn, 'click', this.startBreak);
    this.addEventListener(skipBreakBtn, 'click', this.skipBreak);

    // Overlay click to start break (user-friendly)
    this.addEventListener(this.overlay, 'click', this.startBreak);

    // Prevent dialog clicks from closing
    this.addEventListener(this.dialog, 'click', (e) => e.stopPropagation());

    // Keyboard shortcuts
    this.addEventListener(document, 'keydown', this.handleKeydown);
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeydown(e) {
    if (!this.isVisible) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        this.startBreak();
        break;
      case 'Escape':
      case 's':
      case 'S':
        e.preventDefault();
        this.skipBreak();
        break;
    }
  }

  /**
   * Start break session
   */
  startBreak() {
    if (this.app?.confirmBreak) {
      this.app.confirmBreak();
      this.hide();
    }
  }

  /**
   * Skip break session
   */
  skipBreak() {
    if (this.app?.skipBreak) {
      this.app.skipBreak();
      this.hide();
    }
  }

  /**
   * Show break confirmation dialog
   */
  show(breakData) {
    if (this.isVisible) return;

    this.isVisible = true;
    this.updateBreakInfo(breakData);
    
    this.addClass(this.overlay, 'show');
    this.addClass(this.dialog, 'show');
    
    // Start updating delay time
    this.startDelayTimer();
    
    // Focus start button for accessibility
    const startBtn = this.dialog.querySelector('#start-break-btn');
    if (startBtn) {
      startBtn.focus();
    }
  }

  /**
   * Hide break confirmation dialog
   */
  hide() {
    if (!this.isVisible) return;

    this.isVisible = false;
    this.removeClass(this.overlay, 'show');
    this.removeClass(this.dialog, 'show');
    
    this.stopDelayTimer();
  }

  /**
   * Update break information
   */
  updateBreakInfo(breakData) {
    if (!this.dialog) return;

    const breakTypeEl = this.dialog.querySelector('.break-type');
    const breakMessageEl = this.dialog.querySelector('.break-message');
    
    const breakType = breakData.breakType === 'longBreak' ? 'long break' : 'short break';
    
    if (breakTypeEl) {
      breakTypeEl.textContent = breakType;
    }
    
    if (breakMessageEl) {
      breakMessageEl.innerHTML = `Great job! Time for a <span class="break-type">${breakType}</span>.`;
    }
  }

  /**
   * Start delay timer
   */
  startDelayTimer() {
    this.stopDelayTimer();
    
    this.updateInterval = setInterval(() => {
      this.updateDelayDisplay();
    }, 1000);
  }

  /**
   * Stop delay timer
   */
  stopDelayTimer() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * Update delay time display
   */
  updateDelayDisplay() {
    if (!this.app?.session || !this.dialog) return;

    const pendingBreak = this.app.session.getPendingBreak();
    if (!pendingBreak) return;

    const delayTimeEl = this.dialog.querySelector('.delay-time');
    const bonusTimeEl = this.dialog.querySelector('.bonus-time');
    
    if (delayTimeEl) {
      const minutes = Math.floor(pendingBreak.delayTime / 60);
      const seconds = pendingBreak.delayTime % 60;
      delayTimeEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }
    
    if (bonusTimeEl) {
      const bonusMinutes = Math.floor(pendingBreak.potentialBonusTime / 60);
      const bonusSeconds = pendingBreak.potentialBonusTime % 60;
      bonusTimeEl.textContent = `+${bonusMinutes.toString().padStart(2, '0')}:${bonusSeconds.toString().padStart(2, '0')}`;
      
      // Add visual feedback for bonus time
      if (pendingBreak.potentialBonusTime > 0) {
        this.addClass(bonusTimeEl, 'has-bonus');
      } else {
        this.removeClass(bonusTimeEl, 'has-bonus');
      }
    }
  }

  /**
   * Handle application events
   */
  handleAppEvent(event, data) {
    switch (event) {
      case 'breakPending':
        this.show(data);
        break;
        
      case 'breakConfirmed':
      case 'breakSkipped':
        this.hide();
        break;
        
      case 'timerStart':
        if (this.isVisible) {
          this.hide();
        }
        break;
    }
  }

  /**
   * Cleanup
   */
  destroy() {
    this.stopDelayTimer();
    this.hide();
    
    if (this.overlay && this.overlay.parentNode) {
      this.overlay.parentNode.removeChild(this.overlay);
    }
    
    if (this.dialog && this.dialog.parentNode) {
      this.dialog.parentNode.removeChild(this.dialog);
    }
    
    super.destroy();
  }
}
