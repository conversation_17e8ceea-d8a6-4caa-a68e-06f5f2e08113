/**
 * BreakConfirmation Component - Shows break confirmation dialog
 */
import { BaseComponent } from './BaseComponent.js';

export class BreakConfirmation extends BaseComponent {
  constructor(selector, app) {
    super(selector, app);

    this.isVisible = false;
    this.timerDisplayElement = null;
    this.originalContent = null;
    this.updateInterval = null;
    this.graceTimeout = null;
    this.gracePeriodSeconds = 10; // 10 second grace period
    this.isInGracePeriod = false;
  }

  render() {
    // This component will replace content in timer display, no initial render needed
    console.log('💬 BreakConfirmation component initialized');
  }

  bindEvents() {
    // Keyboard shortcuts
    this.addEventListener(document, 'keydown', this.handleKeydown);
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeydown(e) {
    if (!this.isVisible) return;

    switch (e.key) {
      case 'Enter':
      case ' ':
        e.preventDefault();
        this.startBreak();
        break;
      case 'Escape':
      case 's':
      case 'S':
        e.preventDefault();
        this.skipBreak();
        break;
    }
  }

  /**
   * Start break session
   */
  startBreak() {
    if (this.app?.confirmBreak) {
      this.app.confirmBreak();
      this.hide();
    }
  }

  /**
   * Skip break session
   */
  skipBreak() {
    if (this.app?.skipBreak) {
      this.app.skipBreak();
      this.hide();
    }
  }

  /**
   * Show break confirmation by replacing timer display content
   */
  show(breakData) {
    if (this.isVisible) return;

    console.log('💬 SHOWING BREAK CONFIRMATION', {
      breakType: breakData.type || breakData.breakType,
      timestamp: new Date().toLocaleTimeString()
    });

    this.isVisible = true;

    // Find timer display element
    this.timerDisplayElement = document.querySelector('#timer-display .timer-display');
    if (!this.timerDisplayElement) {
      console.error('💬 Timer display element not found');
      return;
    }

    // Save original content
    this.originalContent = this.timerDisplayElement.innerHTML;

    // Replace with break confirmation content
    this.replaceWithBreakContent(breakData);

    // Start grace period before counting delay time
    this.startGracePeriod();

    // Focus start button for accessibility
    setTimeout(() => {
      const startBtn = document.querySelector('#start-break-btn');
      if (startBtn) {
        startBtn.focus();
      }
    }, 100);
  }

  /**
   * Hide break confirmation and restore timer display
   */
  hide() {
    if (!this.isVisible) return;

    console.log('💬 HIDING BREAK CONFIRMATION');
    this.isVisible = false;
    this.stopDelayTimer();
    this.stopGracePeriod();

    // Restore original timer content
    if (this.timerDisplayElement && this.originalContent) {
      this.timerDisplayElement.innerHTML = this.originalContent;
      console.log('💬 Timer display content restored');
    }

    // Clean up
    this.timerDisplayElement = null;
    this.originalContent = null;
  }

  /**
   * Replace timer display with break confirmation content
   */
  replaceWithBreakContent(breakData) {
    if (!this.timerDisplayElement) return;

    const breakType = breakData.type === 'longBreak' || breakData.breakType === 'longBreak' ? 'long break' : 'short break';

    this.timerDisplayElement.innerHTML = `
      <div class="break-confirmation-content">
        <div class="break-header">
          <div class="break-icon">
            <svg viewBox="0 0 24 24" width="64" height="64">
              <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z" fill="currentColor"/>
            </svg>
          </div>
          <h2 class="break-title">Work Session Complete! 🎉</h2>
          <p class="break-message">Great job! Time for a <span class="break-type">${breakType}</span>.</p>
          <p class="break-subtitle">Take your time - any delay will be added as bonus break time!</p>
        </div>

        <div class="break-stats">
          <div class="stat-item">
            <span class="stat-label">Grace Break</span>
            <span class="delay-time">00:00</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Bonus Time:</span>
            <span class="bonus-time">+00:00</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">Total</span>
            <span class="total-break-time">05:00</span>
          </div>
        </div>

        <div class="break-actions">
          <button class="break-btn secondary" id="skip-break-btn">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill="currentColor"/>
            </svg>
            Skip Break
          </button>

          <button class="break-btn primary" id="start-break-btn">
            <svg viewBox="0 0 24 24" width="20" height="20">
              <path d="M8 5v14l11-7z" fill="currentColor"/>
            </svg>
            Start Break
          </button>
        </div>
      </div>
    `;

    // Bind button events
    this.bindBreakButtons();

    console.log('💬 Break confirmation content displayed in main card');
  }

  /**
   * Bind break confirmation button events
   */
  bindBreakButtons() {
    const startBreakBtn = document.querySelector('#start-break-btn');
    const skipBreakBtn = document.querySelector('#skip-break-btn');

    if (startBreakBtn) {
      startBreakBtn.addEventListener('click', () => {
        console.log('💬 START BREAK BUTTON CLICKED');
        // Use same logic as regular start button
        if (this.app) {
          this.app.confirmBreak();
        }
      });
    }

    if (skipBreakBtn) {
      skipBreakBtn.addEventListener('click', () => {
        console.log('💬 SKIP BREAK BUTTON CLICKED');
        // Use same logic as regular skip button
        if (this.app) {
          this.app.skipBreak();
        }
      });
    }
  }

  /**
   * Update break information
   */
  updateBreakInfo(breakData) {
    if (!this.dialog) return;

    const breakTypeEl = this.dialog.querySelector('.break-type');
    const breakMessageEl = this.dialog.querySelector('.break-message');
    
    const breakType = breakData.breakType === 'longBreak' ? 'long break' : 'short break';
    
    if (breakTypeEl) {
      breakTypeEl.textContent = breakType;
    }
    
    if (breakMessageEl) {
      breakMessageEl.innerHTML = `Great job! Time for a <span class="break-type">${breakType}</span>.`;
    }
  }

  /**
   * Start grace period before counting delay time
   */
  startGracePeriod() {
    console.log(`💬 STARTING ${this.gracePeriodSeconds} SECOND GRACE PERIOD`);
    this.isInGracePeriod = true;

    // Update display to show grace period
    this.updateGracePeriodDisplay();

    // Start grace period countdown
    this.graceTimeout = setTimeout(() => {
      console.log('💬 GRACE PERIOD ENDED - Starting delay timer');
      this.isInGracePeriod = false;
      this.startDelayTimer();
    }, this.gracePeriodSeconds * 1000);
  }

  /**
   * Stop grace period
   */
  stopGracePeriod() {
    if (this.graceTimeout) {
      clearTimeout(this.graceTimeout);
      this.graceTimeout = null;
    }
    this.isInGracePeriod = false;
  }

  /**
   * Start delay timer (after grace period)
   */
  startDelayTimer() {
    if (this.updateInterval) return;

    console.log('💬 STARTING DELAY TIMER');
    this.updateInterval = setInterval(() => {
      if (this.app?.session) {
        this.app.session.incrementDelayTime();
        this.updateDelayDisplay();
      }
    }, 1000);
  }

  /**
   * Stop delay timer
   */
  stopDelayTimer() {
    if (this.updateInterval) {
      clearInterval(this.updateInterval);
      this.updateInterval = null;
    }
  }

  /**
   * Update grace period display
   */
  updateGracePeriodDisplay() {
    const delayTimeEl = document.querySelector('.delay-time');
    const bonusTimeEl = document.querySelector('.bonus-time');
    const totalBreakTimeEl = document.querySelector('.total-break-time');

    if (delayTimeEl) {
      delayTimeEl.textContent = '00:00';
      delayTimeEl.style.color = 'var(--color-text-secondary)';
    }

    if (bonusTimeEl) {
      bonusTimeEl.textContent = '+00:00';
      bonusTimeEl.classList.remove('has-bonus');
      bonusTimeEl.style.color = 'var(--color-text-secondary)';
    }

    if (totalBreakTimeEl) {
      const pendingBreak = this.app?.session?.getPendingBreak();
      if (pendingBreak) {
        const baseBreakTime = pendingBreak.type === 'longBreak' ?
          this.app.settings.get('longBreakDuration') :
          this.app.settings.get('shortBreakDuration');

        const totalMinutes = Math.floor(baseBreakTime / 60);
        const totalSeconds = baseBreakTime % 60;
        totalBreakTimeEl.textContent = `${totalMinutes.toString().padStart(2, '0')}:${totalSeconds.toString().padStart(2, '0')}`;
        totalBreakTimeEl.style.color = 'var(--color-text-primary)';
      }
    }

    // Show grace period message with shorter text
    const statLabels = document.querySelectorAll('.stat-item .stat-label');
    if (statLabels.length >= 3) {
      statLabels[0].textContent = 'Grace Break';
      statLabels[0].style.color = 'var(--color-success)';
      statLabels[1].textContent = 'Bonus Time:';
      statLabels[2].textContent = 'Total';
    }
  }

  /**
   * Update delay time display
   */
  updateDelayDisplay() {
    if (!this.app?.session || !this.isVisible) return;

    const pendingBreak = this.app.session.getPendingBreak();
    if (!pendingBreak) return;

    const delayTimeEl = document.querySelector('.delay-time');
    const bonusTimeEl = document.querySelector('.bonus-time');
    const totalBreakTimeEl = document.querySelector('.total-break-time');

    // Restore labels if they were changed during grace period
    const statLabels = document.querySelectorAll('.stat-item .stat-label');
    if (statLabels.length >= 3) {
      if (statLabels[0].textContent === 'Grace Break') {
        statLabels[0].textContent = 'Delay Time:';
        statLabels[0].style.color = 'var(--color-text-secondary)';
        statLabels[1].textContent = 'Bonus Time:';
        statLabels[2].textContent = 'Total Break:';
      }
    }

    if (delayTimeEl) {
      const minutes = Math.floor(pendingBreak.delayTime / 60);
      const seconds = pendingBreak.delayTime % 60;
      delayTimeEl.textContent = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
      delayTimeEl.style.color = 'var(--color-text-primary)';
    }

    if (bonusTimeEl) {
      const bonusMinutes = Math.floor(pendingBreak.potentialBonusTime / 60);
      const bonusSeconds = pendingBreak.potentialBonusTime % 60;
      bonusTimeEl.textContent = `+${bonusMinutes.toString().padStart(2, '0')}:${bonusSeconds.toString().padStart(2, '0')}`;
      bonusTimeEl.style.color = 'var(--color-text-primary)';

      // Add visual feedback for bonus time
      if (pendingBreak.potentialBonusTime > 0) {
        bonusTimeEl.classList.add('has-bonus');
      } else {
        bonusTimeEl.classList.remove('has-bonus');
      }
    }

    // Update total break time
    if (totalBreakTimeEl) {
      const baseBreakTime = pendingBreak.type === 'longBreak' ?
        this.app.settings.get('longBreakDuration') :
        this.app.settings.get('shortBreakDuration');

      const totalBreakTime = baseBreakTime + pendingBreak.potentialBonusTime;
      const totalMinutes = Math.floor(totalBreakTime / 60);
      const totalSeconds = totalBreakTime % 60;
      totalBreakTimeEl.textContent = `${totalMinutes.toString().padStart(2, '0')}:${totalSeconds.toString().padStart(2, '0')}`;
      totalBreakTimeEl.style.color = 'var(--color-text-primary)';

      // Add visual feedback for total time
      if (pendingBreak.potentialBonusTime > 0) {
        totalBreakTimeEl.classList.add('has-bonus');
      } else {
        totalBreakTimeEl.classList.remove('has-bonus');
      }
    }
  }

  /**
   * Handle application events
   */
  handleAppEvent(event, data) {
    switch (event) {
      case 'breakPending':
        this.show(data);
        break;
        
      case 'breakConfirmed':
      case 'breakSkipped':
        this.hide();
        break;
        
      case 'timerStart':
        if (this.isVisible) {
          this.hide();
        }
        break;
    }
  }

  /**
   * Cleanup
   */
  destroy() {
    this.stopDelayTimer();
    this.stopGracePeriod();
    this.hide();

    super.destroy();
  }
}
