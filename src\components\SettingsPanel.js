/**
 * SettingsPanel Component - Settings configuration panel
 */
import { BaseComponent } from './BaseComponent.js';

export class SettingsPanel extends BaseComponent {
  constructor(selector, app) {
    super(selector, app);

    this.isOpen = false;
    this.overlay = null;
    this.panel = null;

    // Create element if not found
    if (!this.element) {
      console.log('⚙️ Creating settings panel element');
      this.element = document.createElement('div');
      this.element.id = 'settings-panel';
      document.body.appendChild(this.element);
    }
  }

  render() {
    console.log('⚙️ SETTINGS PANEL RENDER', {
      elementExists: !!this.element,
      timestamp: new Date().toLocaleTimeString()
    });

    if (!this.element) {
      console.error('⚙️ Cannot render settings panel - element not found');
      return;
    }

    // Create overlay
    this.overlay = this.createElement('div', 'settings-overlay');
    document.body.appendChild(this.overlay);
    console.log('⚙️ Settings overlay created');

    // Create settings panel
    this.panel = this.createElement('div', 'settings-panel');
    console.log('⚙️ Settings panel element created');

    this.panel.innerHTML = `
      <div class="settings-header">
        <h2 class="settings-title">Settings</h2>
        <button class="settings-close" aria-label="Close settings">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <div class="settings-content">
        <!-- Timer Settings -->
        <section class="settings-section">
          <h3 class="section-title">Timer Duration</h3>
          
          <div class="setting-item">
            <label for="work-duration">Work Session (minutes)</label>
            <input type="number" id="work-duration" min="1" max="60" value="25">
          </div>
          
          <div class="setting-item">
            <label for="short-break-duration">Short Break (minutes)</label>
            <input type="number" id="short-break-duration" min="1" max="30" value="5">
          </div>
          
          <div class="setting-item">
            <label for="long-break-duration">Long Break (minutes)</label>
            <input type="number" id="long-break-duration" min="1" max="60" value="15">
          </div>
          
          <div class="setting-item">
            <label for="sessions-until-long-break">Sessions until Long Break</label>
            <input type="number" id="sessions-until-long-break" min="2" max="10" value="4">
          </div>
        </section>

        <!-- Automation Settings -->
        <section class="settings-section">
          <h3 class="section-title">Automation</h3>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="auto-start-breaks">
              <span class="checkmark"></span>
              Auto-start breaks
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="auto-start-work">
              <span class="checkmark"></span>
              Auto-start work sessions
            </label>
          </div>
        </section>

        <!-- Music Settings -->
        <section class="settings-section">
          <h3 class="section-title">Focus Music</h3>

          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="enable-music">
              <span class="checkmark"></span>
              Enable background music during focus
            </label>
          </div>

          <div class="setting-item">
            <label for="music-track">Music Track</label>
            <select id="music-track">
              <option value="">Loading music...</option>
            </select>
          </div>

          <div class="setting-item">
            <label for="music-volume">Music Volume</label>
            <div class="volume-control">
              <input type="range" id="music-volume" min="0" max="100" value="30">
              <span class="volume-value">30%</span>
            </div>
          </div>
        </section>

        <!-- Appearance Settings -->
        <section class="settings-section">
          <h3 class="section-title">Appearance</h3>

          <div class="setting-item">
            <label for="background-color">Background Color</label>
            <input type="color" id="background-color" value="#1a1a1a">
          </div>

          <div class="setting-item">
            <label for="background-image">Background Image URL</label>
            <input type="url" id="background-image" placeholder="https://example.com/image.jpg">
          </div>

          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="use-background-image">
              <span class="checkmark"></span>
              Use background image
            </label>
          </div>
        </section>

        <!-- Notification Settings -->
        <section class="settings-section">
          <h3 class="section-title">Notifications</h3>

          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="show-notifications" checked>
              <span class="checkmark"></span>
              Show notifications
            </label>
          </div>

          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="play-sound" checked>
              <span class="checkmark"></span>
              Play notification sounds
            </label>
          </div>
          
          <div class="setting-item">
            <label for="sound-volume">Sound Volume</label>
            <input type="range" id="sound-volume" min="0" max="1" step="0.1" value="0.5">
            <span class="volume-value">50%</span>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="browser-notifications">
              <span class="checkmark"></span>
              Browser notifications
            </label>
          </div>
        </section>

        <!-- Appearance Settings -->
        <section class="settings-section">
          <h3 class="section-title">Appearance</h3>
          
          <div class="setting-item">
            <label for="theme">Theme</label>
            <select id="theme">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="show-progress-bar" checked>
              <span class="checkmark"></span>
              Show progress ring
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="show-session-count" checked>
              <span class="checkmark"></span>
              Show session count
            </label>
          </div>
        </section>

        <!-- Actions -->
        <section class="settings-section">
          <h3 class="section-title">Actions</h3>
          
          <div class="setting-actions">
            <button class="action-btn secondary" id="reset-settings">
              Reset to Defaults
            </button>
            <button class="action-btn secondary" id="test-sound">
              Test Sound
            </button>
          </div>
        </section>
      </div>
    `;

    document.body.appendChild(this.panel);
  }

  bindEvents() {
    if (!this.panel || !this.overlay) return;

    // Close button
    const closeBtn = this.panel.querySelector('.settings-close');
    this.addEventListener(closeBtn, 'click', this.close);

    // Overlay click to close
    this.addEventListener(this.overlay, 'click', this.close);

    // Prevent panel clicks from closing
    this.addEventListener(this.panel, 'click', (e) => e.stopPropagation());

    // Settings inputs
    this.bindSettingsInputs();

    // Action buttons
    const resetBtn = this.panel.querySelector('#reset-settings');
    const testSoundBtn = this.panel.querySelector('#test-sound');
    
    this.addEventListener(resetBtn, 'click', this.resetSettings);
    this.addEventListener(testSoundBtn, 'click', this.testSound);

    // Escape key to close
    this.addEventListener(document, 'keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
  }

  /**
   * Bind settings input events
   */
  bindSettingsInputs() {
    const inputs = this.panel.querySelectorAll('input, select');

    inputs.forEach(input => {
      const eventType = input.type === 'range' ? 'input' : 'change';
      this.addEventListener(input, eventType, () => {
        this.updateSetting(input);
      });
    });

    // Volume display updates
    const soundVolumeSlider = this.panel.querySelector('#sound-volume');
    const soundVolumeValue = this.panel.querySelector('.volume-value');

    this.addEventListener(soundVolumeSlider, 'input', () => {
      const value = Math.round(soundVolumeSlider.value * 100);
      soundVolumeValue.textContent = `${value}%`;
    });

    // Music volume display update
    const musicVolumeSlider = this.panel.querySelector('#music-volume');
    const musicVolumeValue = this.panel.querySelector('.volume-value');

    if (musicVolumeSlider && musicVolumeValue) {
      this.addEventListener(musicVolumeSlider, 'input', () => {
        const value = musicVolumeSlider.value;
        musicVolumeValue.textContent = `${value}%`;
      });
    }

    // Background image toggle
    const useBackgroundImage = this.panel.querySelector('#use-background-image');
    const backgroundImage = this.panel.querySelector('#background-image');

    if (useBackgroundImage && backgroundImage) {
      this.addEventListener(useBackgroundImage, 'change', () => {
        backgroundImage.disabled = !useBackgroundImage.checked;
      });
    }
  }

  /**
   * Update setting value
   */
  updateSetting(input) {
    if (!this.app?.settings) return;

    const settingKey = this.getSettingKey(input.id);
    let value = input.value;

    // Convert values based on input type
    if (input.type === 'checkbox') {
      value = input.checked;
    } else if (input.type === 'number') {
      value = parseInt(value, 10);
      // Convert minutes to seconds for duration settings
      if (input.id.includes('duration')) {
        value = value * 60;
      }
    } else if (input.type === 'range') {
      value = parseFloat(value);
      // Convert music volume to 0-1 range
      if (input.id === 'music-volume') {
        value = value / 100;
      }
    }

    this.app.settings.set(settingKey, value);

    // Handle special settings
    this.handleSpecialSetting(input.id, value);
  }

  /**
   * Handle special settings that need immediate action
   */
  handleSpecialSetting(inputId, value) {
    switch (inputId) {
      case 'enable-music':
        console.log('🎵 MUSIC SETTING CHANGED', {
          enabled: value,
          timestamp: new Date().toLocaleTimeString()
        });
        if (this.app.musicPlayer) {
          this.app.musicPlayer.setEnabled(value);
          if (!value) {
            this.app.musicPlayer.stopFocusMusic();
            console.log('🎵 Music disabled and stopped');
          }
        }
        break;

      case 'music-track':
        if (this.app.musicPlayer && value) {
          this.app.musicPlayer.setTrack(value);
          console.log('🎵 MUSIC TRACK CHANGED', {
            track: value,
            timestamp: new Date().toLocaleTimeString()
          });
        }
        break;

      case 'music-volume':
        if (this.app.musicPlayer) {
          this.app.musicPlayer.setVolume(value / 100);
          console.log('🎵 MUSIC VOLUME CHANGED', {
            volume: value + '%',
            timestamp: new Date().toLocaleTimeString()
          });
        }
        break;

      case 'background-color':
        document.documentElement.style.setProperty('--app-background-color', value);
        console.log('🎨 BACKGROUND COLOR CHANGED', {
          color: value,
          timestamp: new Date().toLocaleTimeString()
        });
        break;

      case 'background-image':
        if (this.app.settings.get('useBackgroundImage')) {
          this.updateBackgroundImage(value);
          console.log('🎨 BACKGROUND IMAGE CHANGED', {
            imageUrl: value,
            timestamp: new Date().toLocaleTimeString()
          });
        }
        break;

      case 'use-background-image':
        if (value) {
          const imageUrl = this.app.settings.get('backgroundImage');
          this.updateBackgroundImage(imageUrl);
          console.log('🎨 BACKGROUND IMAGE ENABLED', {
            imageUrl: imageUrl,
            timestamp: new Date().toLocaleTimeString()
          });
        } else {
          this.removeBackgroundImage();
          console.log('🎨 BACKGROUND IMAGE DISABLED', {
            timestamp: new Date().toLocaleTimeString()
          });
        }
        break;
    }
  }

  /**
   * Update background image
   */
  updateBackgroundImage(imageUrl) {
    if (imageUrl) {
      document.body.style.backgroundImage = `url(${imageUrl})`;
      document.body.style.backgroundSize = 'cover';
      document.body.style.backgroundPosition = 'center';
      document.body.style.backgroundRepeat = 'no-repeat';
    }
  }

  /**
   * Remove background image
   */
  removeBackgroundImage() {
    document.body.style.backgroundImage = '';
    document.body.style.backgroundSize = '';
    document.body.style.backgroundPosition = '';
    document.body.style.backgroundRepeat = '';
  }

  /**
   * Map input ID to setting key
   */
  getSettingKey(inputId) {
    const mapping = {
      'work-duration': 'workDuration',
      'short-break-duration': 'shortBreakDuration',
      'long-break-duration': 'longBreakDuration',
      'sessions-until-long-break': 'sessionsUntilLongBreak',
      'auto-start-breaks': 'autoStartBreaks',
      'auto-start-work': 'autoStartWork',
      'show-notifications': 'showNotifications',
      'play-sound': 'playSound',
      'sound-volume': 'soundVolume',
      'browser-notifications': 'browserNotifications',
      'enable-music': 'enableMusic',
      'music-track': 'musicTrack',
      'music-volume': 'musicVolume',
      'background-color': 'backgroundColor',
      'background-image': 'backgroundImage',
      'use-background-image': 'useBackgroundImage',
      'theme': 'theme',
      'show-progress-bar': 'showProgressBar',
      'show-session-count': 'showSessionCount'
    };
    
    return mapping[inputId] || inputId;
  }

  /**
   * Load current settings into form
   */
  async loadSettings() {
    if (!this.app?.settings || !this.panel) return;

    const settings = this.app.settings.getAll();

    // Duration settings (convert seconds to minutes)
    this.setInputValue('work-duration', settings.workDuration / 60);
    this.setInputValue('short-break-duration', settings.shortBreakDuration / 60);
    this.setInputValue('long-break-duration', settings.longBreakDuration / 60);
    this.setInputValue('sessions-until-long-break', settings.sessionsUntilLongBreak);

    // Boolean settings
    this.setInputValue('auto-start-breaks', settings.autoStartBreaks);
    this.setInputValue('auto-start-work', settings.autoStartWork);
    this.setInputValue('show-notifications', settings.showNotifications);
    this.setInputValue('play-sound', settings.playSound);
    this.setInputValue('browser-notifications', settings.browserNotifications);
    this.setInputValue('show-progress-bar', settings.showProgressBar);
    this.setInputValue('show-session-count', settings.showSessionCount);

    // Music settings
    await this.loadMusicOptions();
    this.setInputValue('enable-music', settings.enableMusic || false);
    this.setInputValue('music-track', settings.musicTrack || '');
    this.setInputValue('music-volume', Math.round((settings.musicVolume || 0.3) * 100));

    // Appearance settings
    this.setInputValue('background-color', settings.backgroundColor || '#1a1a1a');
    this.setInputValue('background-image', settings.backgroundImage || '');
    this.setInputValue('use-background-image', settings.useBackgroundImage || false);

    // Other settings
    this.setInputValue('theme', settings.theme);
    this.setInputValue('sound-volume', settings.soundVolume);

    // Update volume displays
    const soundVolumeValue = this.panel.querySelector('.volume-value');
    if (soundVolumeValue) {
      soundVolumeValue.textContent = `${Math.round(settings.soundVolume * 100)}%`;
    }

    const musicVolumeValue = this.panel.querySelectorAll('.volume-value')[1];
    if (musicVolumeValue) {
      musicVolumeValue.textContent = `${Math.round((settings.musicVolume || 0.3) * 100)}%`;
    }

    // Apply background settings
    if (settings.useBackgroundImage && settings.backgroundImage) {
      this.updateBackgroundImage(settings.backgroundImage);
    }
    if (settings.backgroundColor) {
      document.documentElement.style.setProperty('--app-background-color', settings.backgroundColor);
    }
  }

  /**
   * Load music options from music player
   */
  async loadMusicOptions() {
    const musicSelect = this.panel.querySelector('#music-track');
    if (!musicSelect || !this.app?.musicPlayer) return;

    const musicList = this.app.musicPlayer.getMusicList();

    // Clear existing options
    musicSelect.innerHTML = '<option value="">No music</option>';

    // Add music options
    musicList.forEach(track => {
      const option = document.createElement('option');
      option.value = track.id;
      option.textContent = track.name;
      option.title = track.description;
      musicSelect.appendChild(option);
    });
  }

  /**
   * Set input value helper
   */
  setInputValue(id, value) {
    const input = this.panel.querySelector(`#${id}`);
    if (!input) return;

    if (input.type === 'checkbox') {
      input.checked = value;
    } else {
      input.value = value;
    }
  }

  /**
   * Open settings panel
   */
  async open() {
    console.log('⚙️ SETTINGS PANEL OPEN ATTEMPT', {
      isOpen: this.isOpen,
      panelExists: !!this.panel,
      overlayExists: !!this.overlay,
      timestamp: new Date().toLocaleTimeString()
    });

    if (this.isOpen || !this.panel) {
      console.log('⚙️ Settings panel open cancelled:', {
        reason: this.isOpen ? 'already open' : 'panel not found'
      });
      return;
    }

    this.isOpen = true;
    console.log('⚙️ Loading settings...');
    await this.loadSettings();

    console.log('⚙️ Showing settings panel...');
    this.addClass(this.overlay, 'open');
    this.addClass(this.panel, 'open');

    // Focus first input for accessibility
    const firstInput = this.panel?.querySelector('input, select');
    if (firstInput) {
      firstInput.focus();
      console.log('⚙️ Focused first input for accessibility');
    }

    console.log('⚙️ SETTINGS PANEL OPENED SUCCESSFULLY');
  }

  /**
   * Close settings panel
   */
  close() {
    console.log('⚙️ SETTINGS PANEL CLOSE ATTEMPT', {
      isOpen: this.isOpen,
      panelExists: !!this.panel,
      timestamp: new Date().toLocaleTimeString()
    });

    if (!this.isOpen || !this.panel) {
      console.log('⚙️ Settings panel close cancelled:', {
        reason: !this.isOpen ? 'already closed' : 'panel not found'
      });
      return;
    }

    this.isOpen = false;
    this.removeClass(this.overlay, 'open');
    this.removeClass(this.panel, 'open');

    console.log('⚙️ SETTINGS PANEL CLOSED SUCCESSFULLY');
  }

  /**
   * Reset settings to defaults
   */
  resetSettings() {
    if (!this.app?.settings) return;

    if (confirm('Reset all settings to default values?')) {
      this.app.settings.reset();
      this.loadSettings();
    }
  }

  /**
   * Test notification sound
   */
  testSound() {
    if (this.app?.playNotificationSound) {
      this.app.playNotificationSound();
    }
  }

  /**
   * Handle application events
   */
  handleAppEvent(event, data) {
    switch (event) {
      case 'settingsChange':
        if (this.isOpen) {
          this.loadSettings();
        }
        break;
    }
  }

  /**
   * Toggle panel visibility
   */
  toggle() {
    console.log('⚙️ SETTINGS PANEL TOGGLE', {
      currentState: this.isOpen ? 'open' : 'closed',
      panelExists: !!this.panel,
      overlayExists: !!this.overlay,
      timestamp: new Date().toLocaleTimeString()
    });

    if (this.isOpen) {
      console.log('⚙️ Closing settings panel');
      this.close();
    } else {
      console.log('⚙️ Opening settings panel');
      this.open();
    }
  }
}
