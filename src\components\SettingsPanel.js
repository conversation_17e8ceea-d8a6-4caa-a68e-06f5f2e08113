/**
 * SettingsPanel Component - Settings configuration panel
 */
import { BaseComponent } from './BaseComponent.js';

export class SettingsPanel extends BaseComponent {
  constructor(selector, app) {
    super(selector, app);
    
    this.isOpen = false;
    this.overlay = null;
    this.panel = null;
  }

  render() {
    if (!this.element) return;

    // Create overlay
    this.overlay = this.createElement('div', 'settings-overlay');
    document.body.appendChild(this.overlay);

    // Create settings panel
    this.panel = this.createElement('div', 'settings-panel');
    this.panel.innerHTML = `
      <div class="settings-header">
        <h2 class="settings-title">Settings</h2>
        <button class="settings-close" aria-label="Close settings">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" fill="currentColor"/>
          </svg>
        </button>
      </div>

      <div class="settings-content">
        <!-- Timer Settings -->
        <section class="settings-section">
          <h3 class="section-title">Timer Duration</h3>
          
          <div class="setting-item">
            <label for="work-duration">Work Session (minutes)</label>
            <input type="number" id="work-duration" min="1" max="60" value="25">
          </div>
          
          <div class="setting-item">
            <label for="short-break-duration">Short Break (minutes)</label>
            <input type="number" id="short-break-duration" min="1" max="30" value="5">
          </div>
          
          <div class="setting-item">
            <label for="long-break-duration">Long Break (minutes)</label>
            <input type="number" id="long-break-duration" min="1" max="60" value="15">
          </div>
          
          <div class="setting-item">
            <label for="sessions-until-long-break">Sessions until Long Break</label>
            <input type="number" id="sessions-until-long-break" min="2" max="10" value="4">
          </div>
        </section>

        <!-- Automation Settings -->
        <section class="settings-section">
          <h3 class="section-title">Automation</h3>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="auto-start-breaks">
              <span class="checkmark"></span>
              Auto-start breaks
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="auto-start-work">
              <span class="checkmark"></span>
              Auto-start work sessions
            </label>
          </div>
        </section>

        <!-- Notification Settings -->
        <section class="settings-section">
          <h3 class="section-title">Notifications</h3>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="show-notifications" checked>
              <span class="checkmark"></span>
              Show notifications
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="play-sound" checked>
              <span class="checkmark"></span>
              Play notification sounds
            </label>
          </div>
          
          <div class="setting-item">
            <label for="sound-volume">Sound Volume</label>
            <input type="range" id="sound-volume" min="0" max="1" step="0.1" value="0.5">
            <span class="volume-value">50%</span>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="browser-notifications">
              <span class="checkmark"></span>
              Browser notifications
            </label>
          </div>
        </section>

        <!-- Appearance Settings -->
        <section class="settings-section">
          <h3 class="section-title">Appearance</h3>
          
          <div class="setting-item">
            <label for="theme">Theme</label>
            <select id="theme">
              <option value="light">Light</option>
              <option value="dark">Dark</option>
              <option value="auto">Auto (System)</option>
            </select>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="show-progress-bar" checked>
              <span class="checkmark"></span>
              Show progress ring
            </label>
          </div>
          
          <div class="setting-item">
            <label class="checkbox-label">
              <input type="checkbox" id="show-session-count" checked>
              <span class="checkmark"></span>
              Show session count
            </label>
          </div>
        </section>

        <!-- Actions -->
        <section class="settings-section">
          <h3 class="section-title">Actions</h3>
          
          <div class="setting-actions">
            <button class="action-btn secondary" id="reset-settings">
              Reset to Defaults
            </button>
            <button class="action-btn secondary" id="test-sound">
              Test Sound
            </button>
          </div>
        </section>
      </div>
    `;

    document.body.appendChild(this.panel);
  }

  bindEvents() {
    if (!this.panel || !this.overlay) return;

    // Close button
    const closeBtn = this.panel.querySelector('.settings-close');
    this.addEventListener(closeBtn, 'click', this.close);

    // Overlay click to close
    this.addEventListener(this.overlay, 'click', this.close);

    // Prevent panel clicks from closing
    this.addEventListener(this.panel, 'click', (e) => e.stopPropagation());

    // Settings inputs
    this.bindSettingsInputs();

    // Action buttons
    const resetBtn = this.panel.querySelector('#reset-settings');
    const testSoundBtn = this.panel.querySelector('#test-sound');
    
    this.addEventListener(resetBtn, 'click', this.resetSettings);
    this.addEventListener(testSoundBtn, 'click', this.testSound);

    // Escape key to close
    this.addEventListener(document, 'keydown', (e) => {
      if (e.key === 'Escape' && this.isOpen) {
        this.close();
      }
    });
  }

  /**
   * Bind settings input events
   */
  bindSettingsInputs() {
    const inputs = this.panel.querySelectorAll('input, select');
    
    inputs.forEach(input => {
      const eventType = input.type === 'range' ? 'input' : 'change';
      this.addEventListener(input, eventType, () => {
        this.updateSetting(input);
      });
    });

    // Volume display update
    const volumeSlider = this.panel.querySelector('#sound-volume');
    const volumeValue = this.panel.querySelector('.volume-value');
    
    this.addEventListener(volumeSlider, 'input', () => {
      const value = Math.round(volumeSlider.value * 100);
      volumeValue.textContent = `${value}%`;
    });
  }

  /**
   * Update setting value
   */
  updateSetting(input) {
    if (!this.app?.settings) return;

    const settingKey = this.getSettingKey(input.id);
    let value = input.value;

    // Convert values based on input type
    if (input.type === 'checkbox') {
      value = input.checked;
    } else if (input.type === 'number') {
      value = parseInt(value, 10);
      // Convert minutes to seconds for duration settings
      if (input.id.includes('duration')) {
        value = value * 60;
      }
    } else if (input.type === 'range') {
      value = parseFloat(value);
    }

    this.app.settings.set(settingKey, value);
  }

  /**
   * Map input ID to setting key
   */
  getSettingKey(inputId) {
    const mapping = {
      'work-duration': 'workDuration',
      'short-break-duration': 'shortBreakDuration',
      'long-break-duration': 'longBreakDuration',
      'sessions-until-long-break': 'sessionsUntilLongBreak',
      'auto-start-breaks': 'autoStartBreaks',
      'auto-start-work': 'autoStartWork',
      'show-notifications': 'showNotifications',
      'play-sound': 'playSound',
      'sound-volume': 'soundVolume',
      'browser-notifications': 'browserNotifications',
      'theme': 'theme',
      'show-progress-bar': 'showProgressBar',
      'show-session-count': 'showSessionCount'
    };
    
    return mapping[inputId] || inputId;
  }

  /**
   * Load current settings into form
   */
  loadSettings() {
    if (!this.app?.settings || !this.panel) return;

    const settings = this.app.settings.getAll();
    
    // Duration settings (convert seconds to minutes)
    this.setInputValue('work-duration', settings.workDuration / 60);
    this.setInputValue('short-break-duration', settings.shortBreakDuration / 60);
    this.setInputValue('long-break-duration', settings.longBreakDuration / 60);
    this.setInputValue('sessions-until-long-break', settings.sessionsUntilLongBreak);
    
    // Boolean settings
    this.setInputValue('auto-start-breaks', settings.autoStartBreaks);
    this.setInputValue('auto-start-work', settings.autoStartWork);
    this.setInputValue('show-notifications', settings.showNotifications);
    this.setInputValue('play-sound', settings.playSound);
    this.setInputValue('browser-notifications', settings.browserNotifications);
    this.setInputValue('show-progress-bar', settings.showProgressBar);
    this.setInputValue('show-session-count', settings.showSessionCount);
    
    // Other settings
    this.setInputValue('theme', settings.theme);
    this.setInputValue('sound-volume', settings.soundVolume);
    
    // Update volume display
    const volumeValue = this.panel.querySelector('.volume-value');
    if (volumeValue) {
      volumeValue.textContent = `${Math.round(settings.soundVolume * 100)}%`;
    }
  }

  /**
   * Set input value helper
   */
  setInputValue(id, value) {
    const input = this.panel.querySelector(`#${id}`);
    if (!input) return;

    if (input.type === 'checkbox') {
      input.checked = value;
    } else {
      input.value = value;
    }
  }

  /**
   * Open settings panel
   */
  open() {
    if (this.isOpen) return;

    this.isOpen = true;
    this.loadSettings();
    
    this.addClass(this.overlay, 'open');
    this.addClass(this.panel, 'open');
    
    // Focus first input for accessibility
    const firstInput = this.panel.querySelector('input, select');
    if (firstInput) {
      firstInput.focus();
    }
  }

  /**
   * Close settings panel
   */
  close() {
    if (!this.isOpen) return;

    this.isOpen = false;
    this.removeClass(this.overlay, 'open');
    this.removeClass(this.panel, 'open');
  }

  /**
   * Reset settings to defaults
   */
  resetSettings() {
    if (!this.app?.settings) return;

    if (confirm('Reset all settings to default values?')) {
      this.app.settings.reset();
      this.loadSettings();
    }
  }

  /**
   * Test notification sound
   */
  testSound() {
    if (this.app?.playNotificationSound) {
      this.app.playNotificationSound();
    }
  }

  /**
   * Handle application events
   */
  handleAppEvent(event, data) {
    switch (event) {
      case 'settingsChange':
        if (this.isOpen) {
          this.loadSettings();
        }
        break;
    }
  }

  /**
   * Toggle panel visibility
   */
  toggle() {
    if (this.isOpen) {
      this.close();
    } else {
      this.open();
    }
  }
}
