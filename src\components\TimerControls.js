/**
 * TimerControls Component - Control buttons for timer functionality
 */
import { BaseComponent } from './BaseComponent.js';

export class TimerControls extends BaseComponent {
  constructor(selector, app) {
    super(selector, app);
    
    this.startButton = null;
    this.pauseButton = null;
    this.resetButton = null;
    this.skipButton = null;
    
    this.isRunning = false;
    this.isPaused = false;
  }

  render() {
    if (!this.element) return;

    this.element.innerHTML = `
      <div class="timer-controls">
        <button class="control-btn start-btn" data-action="start">
          <svg class="btn-icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M8 5v14l11-7z" fill="currentColor"/>
          </svg>
          <span class="btn-text">Start</span>
        </button>
        
        <button class="control-btn pause-btn" data-action="pause" style="display: none;">
          <svg class="btn-icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M6 19h4V5H6v14zm8-14v14h4V5h-4z" fill="currentColor"/>
          </svg>
          <span class="btn-text">Pause</span>
        </button>
        
        <button class="control-btn reset-btn" data-action="reset">
          <svg class="btn-icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 5V1L7 6l5 5V7c3.31 0 6 2.69 6 6s-2.69 6-6 6-6-2.69-6-6H4c0 4.42 3.58 8 8 8s8-3.58 8-8-3.58-8-8-8z" fill="currentColor"/>
          </svg>
          <span class="btn-text">Reset</span>
        </button>
        
        <button class="control-btn skip-btn" data-action="skip">
          <svg class="btn-icon" viewBox="0 0 24 24" width="24" height="24">
            <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill="currentColor"/>
          </svg>
          <span class="btn-text">Skip</span>
        </button>
      </div>
    `;

    // Cache DOM elements
    this.startButton = this.element.querySelector('.start-btn');
    this.pauseButton = this.element.querySelector('.pause-btn');
    this.resetButton = this.element.querySelector('.reset-btn');
    this.skipButton = this.element.querySelector('.skip-btn');
  }

  bindEvents() {
    if (!this.element) return;

    // Handle control button clicks
    this.addEventListener(this.element, 'click', this.handleControlClick);
    
    // Handle keyboard shortcuts
    this.addEventListener(document, 'keydown', this.handleKeyboardShortcuts);
  }

  /**
   * Handle control button clicks
   */
  handleControlClick(event) {
    const button = event.target.closest('.control-btn');
    if (!button) return;

    const action = button.dataset.action;
    
    // Add click animation
    this.animateButtonClick(button);
    
    switch (action) {
      case 'start':
        this.handleStart();
        break;
      case 'pause':
        this.handlePause();
        break;
      case 'reset':
        this.handleReset();
        break;
      case 'skip':
        this.handleSkip();
        break;
    }
  }

  /**
   * Handle keyboard shortcuts
   */
  handleKeyboardShortcuts(event) {
    // Only handle shortcuts when not typing in input fields
    if (event.target.tagName === 'INPUT' || event.target.tagName === 'TEXTAREA') {
      return;
    }

    switch (event.code) {
      case 'Space':
        event.preventDefault();
        if (this.isRunning) {
          this.handlePause();
        } else {
          this.handleStart();
        }
        break;
      case 'KeyR':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          this.handleReset();
        }
        break;
      case 'KeyS':
        if (event.ctrlKey || event.metaKey) {
          event.preventDefault();
          this.handleSkip();
        }
        break;
    }
  }

  /**
   * Handle start action
   */
  handleStart() {
    if (this.app && typeof this.app.startTimer === 'function') {
      this.app.startTimer();
    }
    this.emit('timerStart');
  }

  /**
   * Handle pause action
   */
  handlePause() {
    if (this.app && typeof this.app.pauseTimer === 'function') {
      this.app.pauseTimer();
    }
    this.emit('timerPause');
  }

  /**
   * Handle reset action
   */
  handleReset() {
    // Show confirmation if enabled in settings
    if (this.app?.settings?.get('confirmReset') && (this.isRunning || this.isPaused)) {
      if (!confirm('Are you sure you want to reset the timer?')) {
        return;
      }
    }

    if (this.app && typeof this.app.resetTimer === 'function') {
      this.app.resetTimer();
    }
    this.emit('timerReset');
  }

  /**
   * Handle skip action
   */
  handleSkip() {
    if (this.app && typeof this.app.skipSession === 'function') {
      this.app.skipSession();
    }
    this.emit('sessionSkip');
  }

  /**
   * Handle application events
   */
  handleAppEvent(event, data) {
    switch (event) {
      case 'timerStart':
        this.updateButtonStates(true, false);
        break;
        
      case 'timerPause':
        this.updateButtonStates(false, true);
        break;
        
      case 'timerReset':
      case 'timerComplete':
        this.updateButtonStates(false, false);
        break;
        
      case 'sessionChange':
        this.updateButtonStates(false, false);
        break;

      case 'breakPending':
        this.hideAllControls();
        break;

      case 'breakConfirmed':
      case 'breakSkipped':
        this.showAllControls();
        this.updateButtonStates(false, false);
        break;
    }
  }

  /**
   * Update button states based on timer status
   */
  updateButtonStates(isRunning, isPaused) {
    this.isRunning = isRunning;
    this.isPaused = isPaused;

    // Toggle start/pause button visibility
    if (this.startButton && this.pauseButton) {
      if (isRunning) {
        this.hide(this.startButton);
        this.show(this.pauseButton);
      } else {
        this.show(this.startButton);
        this.hide(this.pauseButton);
        
        // Update start button text based on pause state
        const startText = this.startButton.querySelector('.btn-text');
        if (startText) {
          startText.textContent = isPaused ? 'Resume' : 'Start';
        }
      }
    }

    // Update button states
    this.toggleClass(this.element, 'timer-running', isRunning);
    this.toggleClass(this.element, 'timer-paused', isPaused);
    
    // Enable/disable buttons based on state
    if (this.resetButton) {
      this.resetButton.disabled = false; // Reset always available
    }
    
    if (this.skipButton) {
      this.skipButton.disabled = false; // Skip always available
    }
  }

  /**
   * Animate button click
   */
  animateButtonClick(button) {
    button.classList.add('clicked');
    setTimeout(() => {
      button.classList.remove('clicked');
    }, 150);
  }

  /**
   * Enable/disable all controls
   */
  setEnabled(enabled) {
    const buttons = this.element.querySelectorAll('.control-btn');
    buttons.forEach(button => {
      button.disabled = !enabled;
    });
    
    this.toggleClass(this.element, 'disabled', !enabled);
  }

  /**
   * Get current control state
   */
  getControlState() {
    return {
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      buttonsEnabled: !this.element?.classList.contains('disabled')
    };
  }

  /**
   * Update control theme
   */
  updateTheme(theme) {
    this.element.setAttribute('data-theme', theme);
  }

  /**
   * Show break confirmation controls
   */
  showBreakControls() {
    // Hide normal controls
    if (this.startButton) this.hide(this.startButton);
    if (this.pauseButton) this.hide(this.pauseButton);
    if (this.resetButton) this.hide(this.resetButton);
    if (this.skipButton) this.hide(this.skipButton);

    // Create break controls if they don't exist
    if (!this.breakControls) {
      this.createBreakControls();
    }

    // Show break controls
    if (this.breakControls) {
      this.show(this.breakControls);
    }
  }

  /**
   * Hide break confirmation controls
   */
  hideBreakControls() {
    if (this.breakControls) {
      this.hide(this.breakControls);
    }

    // Show normal controls
    if (this.startButton) this.show(this.startButton);
    if (this.resetButton) this.show(this.resetButton);
    if (this.skipButton) this.show(this.skipButton);
  }

  /**
   * Hide all controls during break confirmation
   */
  hideAllControls() {
    console.log('🎮 HIDING ALL TIMER CONTROLS');

    // Hide all normal controls
    if (this.startButton) this.hide(this.startButton);
    if (this.pauseButton) this.hide(this.pauseButton);
    if (this.resetButton) this.hide(this.resetButton);
    if (this.skipButton) this.hide(this.skipButton);

    // Hide break controls if they exist
    if (this.breakControls) this.hide(this.breakControls);

    // Hide the entire controls container
    if (this.element) {
      this.element.style.display = 'none';
    }
  }

  /**
   * Show all controls after break confirmation
   */
  showAllControls() {
    console.log('🎮 SHOWING ALL TIMER CONTROLS');

    // Show the controls container
    if (this.element) {
      this.element.style.display = 'block';
    }

    // Show normal controls
    if (this.startButton) this.show(this.startButton);
    if (this.resetButton) this.show(this.resetButton);
    if (this.skipButton) this.show(this.skipButton);

    // Hide break controls
    if (this.breakControls) this.hide(this.breakControls);
  }

  /**
   * Create break confirmation controls
   */
  createBreakControls() {
    this.breakControls = this.createElement('div', 'break-controls');
    this.breakControls.innerHTML = `
      <button class="control-btn primary" id="confirm-break-btn">
        <svg class="btn-icon" viewBox="0 0 24 24" width="24" height="24">
          <path d="M8 5v14l11-7z" fill="currentColor"/>
        </svg>
        <span class="btn-text">Start Break</span>
      </button>

      <button class="control-btn secondary" id="skip-break-btn">
        <svg class="btn-icon" viewBox="0 0 24 24" width="24" height="24">
          <path d="M6 18l8.5-6L6 6v12zM16 6v12h2V6h-2z" fill="currentColor"/>
        </svg>
        <span class="btn-text">Skip Break</span>
      </button>
    `;

    // Add event listeners
    const confirmBtn = this.breakControls.querySelector('#confirm-break-btn');
    const skipBtn = this.breakControls.querySelector('#skip-break-btn');

    this.addEventListener(confirmBtn, 'click', () => {
      if (this.app?.confirmBreak) {
        this.app.confirmBreak();
      }
    });

    this.addEventListener(skipBtn, 'click', () => {
      if (this.app?.skipBreak) {
        this.app.skipBreak();
      }
    });

    // Insert break controls into the container
    this.element.appendChild(this.breakControls);
    this.hide(this.breakControls);
  }
}
