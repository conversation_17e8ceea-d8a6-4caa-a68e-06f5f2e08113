/**
 * TimerDisplay Component - Displays timer countdown and session information
 */
import { BaseComponent } from './BaseComponent.js';

export class TimerDisplay extends BaseComponent {
  constructor(selector, app) {
    super(selector, app);
    
    this.timeElement = null;
    this.sessionTypeElement = null;
    this.sessionNumberElement = null;
    this.progressBar = null;
    this.progressFill = null;
  }

  render() {
    if (!this.element) return;

    this.element.innerHTML = `
      <div class="timer-display">
        <div class="session-info">
          <span class="session-type">Work Session</span>
          <span class="session-number">#1</span>
        </div>
        
        <div class="timer-circle">
          <svg class="progress-ring" width="300" height="300">
            <circle class="progress-ring-background" 
                    cx="150" cy="150" r="140" 
                    stroke-width="8" 
                    fill="transparent"/>
            <circle class="progress-ring-progress" 
                    cx="150" cy="150" r="140" 
                    stroke-width="8" 
                    fill="transparent"
                    stroke-dasharray="879.646" 
                    stroke-dashoffset="879.646"/>
          </svg>
          <div class="timer-content">
            <div class="timer-time">25:00</div>
            <div class="timer-label">Focus Time</div>
          </div>
        </div>
        
        <div class="session-progress">
          <div class="progress-dots">
            <span class="dot active"></span>
            <span class="dot"></span>
            <span class="dot"></span>
            <span class="dot"></span>
          </div>
        </div>
      </div>
    `;

    // Cache DOM elements
    this.timeElement = this.element.querySelector('.timer-time');
    this.sessionTypeElement = this.element.querySelector('.session-type');
    this.sessionNumberElement = this.element.querySelector('.session-number');
    this.progressRing = this.element.querySelector('.progress-ring-progress');
    this.progressDots = this.element.querySelector('.progress-dots');
    this.timerLabel = this.element.querySelector('.timer-label');
    
    // Calculate circle circumference for progress
    this.circumference = 2 * Math.PI * 140; // radius = 140
    this.progressRing.style.strokeDasharray = this.circumference;
    this.progressRing.style.strokeDashoffset = this.circumference;
  }

  /**
   * Handle application events
   */
  handleAppEvent(event, data) {
    switch (event) {
      case 'timerTick':
        this.updateTime(data.timeRemaining);
        this.updateProgress(data.timer.progress);
        break;
        
      case 'sessionChange':
        console.log('🖥️ TIMER DISPLAY - Session change received:', {
          type: data.type,
          duration: data.duration,
          currentTimeElement: this.timeElement?.textContent
        });
        this.updateSessionInfo(data);
        this.updateProgress(0);
        this.updateTime(data.duration);
        console.log('🖥️ TIMER DISPLAY - Updated to:', this.timeElement?.textContent);
        break;
        
      case 'timerStart':
        this.addClass(this.element, 'timer-running');
        this.removeClass(this.element, 'timer-paused');
        break;
        
      case 'timerPause':
        this.addClass(this.element, 'timer-paused');
        this.removeClass(this.element, 'timer-running');
        break;
        
      case 'timerReset':
        this.removeClass(this.element, 'timer-running timer-paused');
        this.updateProgress(0);
        this.updateTime(data.timeRemaining);
        break;
        
      case 'timerComplete':
        this.removeClass(this.element, 'timer-running timer-paused');
        this.addClass(this.element, 'timer-complete');
        setTimeout(() => {
          this.removeClass(this.element, 'timer-complete');
        }, 2000);
        break;
        
      case 'settingsChange':
        if (data.showProgressBar !== undefined) {
          this.toggleProgressBar(data.showProgressBar.newValue);
        }
        if (data.showSessionCount !== undefined) {
          this.toggleSessionCount(data.showSessionCount.newValue);
        }
        break;
    }
  }

  /**
   * Update displayed time
   */
  updateTime(timeInSeconds) {
    if (this.timeElement) {
      const minutes = Math.floor(timeInSeconds / 60);
      const seconds = timeInSeconds % 60;
      const formattedTime = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

      console.log('🖥️ UPDATE TIME:', {
        timeInSeconds: timeInSeconds,
        formattedTime: formattedTime,
        oldTime: this.timeElement.textContent
      });

      this.updateContent(this.timeElement, formattedTime);

      console.log('🖥️ TIME UPDATED TO:', this.timeElement.textContent);
    }
  }

  /**
   * Update progress ring
   */
  updateProgress(progressPercent) {
    if (this.progressRing) {
      const offset = this.circumference - (progressPercent / 100) * this.circumference;
      this.progressRing.style.strokeDashoffset = offset;
    }
  }

  /**
   * Update session information
   */
  updateSessionInfo(sessionData) {
    // Update session type
    if (this.sessionTypeElement) {
      const sessionTypes = {
        work: 'Work Session',
        shortBreak: 'Short Break',
        longBreak: 'Long Break'
      };
      this.updateContent(this.sessionTypeElement, sessionTypes[sessionData.type] || 'Session');
    }

    // Update session number
    if (this.sessionNumberElement) {
      this.updateContent(this.sessionNumberElement, `#${sessionData.number}`);
    }

    // Update timer label
    if (this.timerLabel) {
      const labels = {
        work: 'Focus Time',
        shortBreak: 'Short Break',
        longBreak: 'Long Break'
      };
      this.updateContent(this.timerLabel, labels[sessionData.type] || 'Timer');
    }

    // Update session type class
    this.element.className = this.element.className.replace(/session-\w+/g, '');
    this.addClass(this.element, `session-${sessionData.type}`);

    // Update progress dots if it's a work session
    if (sessionData.type === 'work') {
      this.updateProgressDots(sessionData.number);
    }
  }

  /**
   * Update progress dots for session tracking
   */
  updateProgressDots(currentSession) {
    if (!this.progressDots) return;

    const dots = this.progressDots.querySelectorAll('.dot');
    dots.forEach((dot, index) => {
      const sessionNumber = index + 1;
      dot.classList.toggle('active', sessionNumber === currentSession);
      dot.classList.toggle('completed', sessionNumber < currentSession);
    });
  }

  /**
   * Toggle progress bar visibility
   */
  toggleProgressBar(show) {
    const progressRing = this.element.querySelector('.progress-ring');
    if (progressRing) {
      this.toggleClass(progressRing, 'hidden', !show);
    }
  }

  /**
   * Toggle session count visibility
   */
  toggleSessionCount(show) {
    const sessionInfo = this.element.querySelector('.session-info');
    if (sessionInfo) {
      this.toggleClass(sessionInfo, 'hidden', !show);
    }
  }

  /**
   * Get current timer display state
   */
  getDisplayState() {
    return {
      time: this.timeElement?.textContent || '00:00',
      sessionType: this.sessionTypeElement?.textContent || '',
      sessionNumber: this.sessionNumberElement?.textContent || '',
      isRunning: this.element?.classList.contains('timer-running') || false,
      isPaused: this.element?.classList.contains('timer-paused') || false
    };
  }

  /**
   * Animate timer completion
   */
  animateCompletion() {
    this.addClass(this.element, 'completion-animation');
    setTimeout(() => {
      this.removeClass(this.element, 'completion-animation');
    }, 1000);
  }
}
