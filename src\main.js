/**
 * Pomodoro Timer Application
 * Main entry point for the application
 */
import './style.css'
import './styles/timer.css'
import './styles/components.css'
import './styles/themes.css'

import { PomodoroApp } from './utils/PomodoroApp.js'
import { TimerDisplay } from './components/TimerDisplay.js'
import { TimerControls } from './components/TimerControls.js'
import { SettingsPanel } from './components/SettingsPanel.js'
import { BreakConfirmation } from './components/BreakConfirmation.js'

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
  initializeApp();
});

/**
 * Initialize the Pomodoro application
 */
function initializeApp() {
  // Create app container HTML
  document.querySelector('#app').innerHTML = `
    <div class="pomodoro-app">
      <header class="app-header">
        <h1 class="app-title">🍅 Pomodoro Timer</h1>
        <button class="settings-btn" id="settings-btn">
          <svg viewBox="0 0 24 24" width="24" height="24">
            <path d="M12 15.5A3.5 3.5 0 0 1 8.5 12A3.5 3.5 0 0 1 12 8.5a3.5 3.5 0 0 1 3.5 3.5 3.5 3.5 0 0 1-3.5 3.5m7.43-2.53c.04-.32.07-.64.07-.97 0-.33-.03-.66-.07-1l2.11-1.63c.19-.15.24-.42.12-.64l-2-3.46c-.12-.22-.39-.31-.61-.22l-2.49 1c-.52-.39-1.06-.73-1.69-.98l-.37-2.65A.506.506 0 0 0 14 2h-4c-.25 0-.46.18-.5.42l-.37 2.65c-.63.25-1.17.59-1.69.98l-2.49-1c-.22-.09-.49 0-.61.22l-2 3.46c-.13.22-.07.49.12.64L4.57 11c-.04.34-.07.67-.07 1 0 .33.03.65.07.97l-2.11 1.66c-.19.15-.25.42-.12.64l2 3.46c.12.22.39.3.61.22l2.49-1.01c.52.4 1.06.74 1.69.99l.37 2.65c.04.24.25.42.5.42h4c.25 0 .46-.18.5-.42l.37-2.65c.63-.26 1.17-.59 1.69-.99l2.49 1.01c.22.08.49 0 .61-.22l2-3.46c.12-.22.07-.49-.12-.64l-2.11-1.66Z" fill="currentColor"/>
          </svg>
        </button>
      </header>

      <main class="app-main">
        <div id="timer-display" class="timer-display-container"></div>
        <div id="timer-controls" class="timer-controls-container"></div>
      </main>

      <footer class="app-footer">
        <div class="session-stats">
          <span class="stat-item">
            <span class="stat-label">Sessions:</span>
            <span class="stat-value" id="session-count">0</span>
          </span>
          <span class="stat-item">
            <span class="stat-label">Focus Time:</span>
            <span class="stat-value" id="focus-time">0h 0m</span>
          </span>
        </div>
      </footer>
    </div>
  `;

  // Initialize the Pomodoro app
  const app = new PomodoroApp();

  // Initialize components
  const timerDisplay = new TimerDisplay('#timer-display', app);
  const timerControls = new TimerControls('#timer-controls', app);
  const settingsPanel = new SettingsPanel('#settings-panel', app);
  const breakConfirmation = new BreakConfirmation('body', app);

  // Initialize components
  timerDisplay.init();
  timerControls.init();
  settingsPanel.init();
  breakConfirmation.init();

  // Settings button event
  const settingsBtn = document.querySelector('#settings-btn');
  if (settingsBtn) {
    console.log('⚙️ Settings button found and event listener attached');
    settingsBtn.addEventListener('click', (e) => {
      console.log('⚙️ SETTINGS BUTTON CLICKED', {
        timestamp: new Date().toLocaleTimeString(),
        panelExists: !!settingsPanel,
        panelElement: !!settingsPanel?.element,
        panelPanel: !!settingsPanel?.panel
      });
      e.preventDefault();
      settingsPanel.toggle();
    });
  } else {
    console.error('❌ Settings button not found!');
  }

  // Initialize the app
  app.init();

  // Store app instance globally for debugging
  window.pomodoroApp = app;

  console.log('Pomodoro Timer initialized successfully!');
}
