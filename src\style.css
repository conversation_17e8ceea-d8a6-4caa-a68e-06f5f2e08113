/**
 * Main Styles for Pomodoro Timer Application
 */

/* CSS Custom Properties for theming */
:root {
  /* Typography */
  --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-size-base: 16px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
  --font-weight-semibold: 600;
  --font-weight-bold: 700;

  /* Spacing */
  --spacing-xs: 0.25rem;
  --spacing-sm: 0.5rem;
  --spacing-md: 1rem;
  --spacing-lg: 1.5rem;
  --spacing-xl: 2rem;
  --spacing-2xl: 3rem;

  /* Border radius */
  --radius-sm: 0.375rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-full: 9999px;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgb(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px -1px rgb(0 0 0 / 0.1), 0 2px 4px -2px rgb(0 0 0 / 0.1);
  --shadow-lg: 0 10px 15px -3px rgb(0 0 0 / 0.1), 0 4px 6px -4px rgb(0 0 0 / 0.1);

  /* Transitions */
  --transition-fast: 150ms ease-in-out;
  --transition-normal: 250ms ease-in-out;
  --transition-slow: 350ms ease-in-out;
}

/* Light theme colors (default) */
:root,
[data-theme="light"] {
  --color-primary: #dc2626;
  --color-primary-hover: #b91c1c;
  --color-primary-light: #fef2f2;

  --color-success: #16a34a;
  --color-success-light: #f0fdf4;

  --color-warning: #d97706;
  --color-warning-light: #fffbeb;

  --color-background: #ffffff;
  --color-surface: #f8fafc;
  --color-surface-hover: #f1f5f9;

  --color-text-primary: #0f172a;
  --color-text-secondary: #64748b;
  --color-text-muted: #94a3b8;

  --color-border: #e2e8f0;
  --color-border-hover: #cbd5e1;

  --color-shadow: rgb(0 0 0 / 0.1);
}

/* Dark theme colors */
[data-theme="dark"] {
  --color-primary: #ef4444;
  --color-primary-hover: #dc2626;
  --color-primary-light: #1f1f1f;

  --color-success: #22c55e;
  --color-success-light: #0f1f0f;

  --color-warning: #f59e0b;
  --color-warning-light: #1f1f0f;

  --color-background: #0f172a;
  --color-surface: #1e293b;
  --color-surface-hover: #334155;

  --color-text-primary: #f8fafc;
  --color-text-secondary: #cbd5e1;
  --color-text-muted: #64748b;

  --color-border: #334155;
  --color-border-hover: #475569;

  --color-shadow: rgb(0 0 0 / 0.3);
}

/* Base styles */
* {
  box-sizing: border-box;
}

html {
  font-size: var(--font-size-base);
  line-height: 1.5;
}

body {
  margin: 0;
  font-family: var(--font-family);
  font-weight: var(--font-weight-normal);
  color: var(--color-text-primary);
  background-color: var(--color-background);
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: var(--spacing-md);

  /* Smooth font rendering */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  text-rendering: optimizeLegibility;
}

/* App container */
#app {
  width: 100%;
  max-width: 480px;
  margin: 0 auto;
}

/* Pomodoro app layout */
.pomodoro-app {
  background: var(--color-surface);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-lg);
  overflow: hidden;
  border: 1px solid var(--color-border);
}

/* Header */
.app-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--color-background);
  border-bottom: 1px solid var(--color-border);
}

.app-title {
  margin: 0;
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.settings-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.settings-btn:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

/* Main content */
.app-main {
  padding: var(--spacing-2xl) var(--spacing-xl);
}

/* Footer */
.app-footer {
  padding: var(--spacing-lg) var(--spacing-xl);
  background: var(--color-background);
  border-top: 1px solid var(--color-border);
}

.session-stats {
  display: flex;
  justify-content: center;
  gap: var(--spacing-xl);
}

.stat-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-xs);
}

.stat-label {
  font-size: 0.875rem;
  color: var(--color-text-secondary);
  font-weight: var(--font-weight-medium);
}

.stat-value {
  font-size: 1.125rem;
  color: var(--color-text-primary);
  font-weight: var(--font-weight-semibold);
}

/* Responsive design */
@media (max-width: 480px) {
  body {
    padding: var(--spacing-sm);
  }

  .app-header {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .app-main {
    padding: var(--spacing-xl) var(--spacing-lg);
  }

  .app-footer {
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .session-stats {
    gap: var(--spacing-lg);
  }
}
