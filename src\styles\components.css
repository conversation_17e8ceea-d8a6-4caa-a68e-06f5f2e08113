/**
 * Component Styles
 */

/* Timer Controls */
.timer-controls-container {
  display: flex;
  justify-content: center;
}

.timer-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.control-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  min-width: 80px;
  justify-content: center;
}

.control-btn:hover:not(:disabled) {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.control-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.clicked {
  transform: scale(0.95);
}

/* Specific button styles */
.start-btn,
.pause-btn {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.start-btn:hover:not(:disabled),
.pause-btn:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.reset-btn {
  background: var(--color-surface);
  color: var(--color-text-secondary);
}

.reset-btn:hover:not(:disabled) {
  color: var(--color-text-primary);
}

.skip-btn {
  background: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.skip-btn:hover:not(:disabled) {
  background: #b45309;
  border-color: #b45309;
}

/* Button icons */
.btn-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.btn-text {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

/* Timer states */
.timer-controls.timer-running .start-btn {
  display: none;
}

.timer-controls.timer-paused .pause-btn {
  display: none;
}

.timer-controls.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Settings Panel */
.settings-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--color-background);
  border-left: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
  transition: right var(--transition-normal);
  z-index: 1000;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
}

.settings-panel.open {
  right: 0;
}

.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 999;
}

.settings-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Settings Header */
.settings-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-lg) var(--spacing-xl);
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
}

.settings-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.settings-close {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: var(--radius-md);
  background: transparent;
  color: var(--color-text-secondary);
  cursor: pointer;
  transition: all var(--transition-fast);
}

.settings-close:hover {
  background: var(--color-surface-hover);
  color: var(--color-text-primary);
}

/* Settings Content */
.settings-content {
  flex: 1;
  padding: var(--spacing-lg) var(--spacing-xl);
  overflow-y: auto;
}

.settings-section {
  margin-bottom: var(--spacing-2xl);
}

.settings-section:last-child {
  margin-bottom: 0;
}

.section-title {
  margin: 0 0 var(--spacing-lg) 0;
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
  border-bottom: 1px solid var(--color-border);
  padding-bottom: var(--spacing-sm);
}

/* Setting Items */
.setting-item {
  margin-bottom: var(--spacing-lg);
}

.setting-item:last-child {
  margin-bottom: 0;
}

.setting-item label {
  display: block;
  margin-bottom: var(--spacing-sm);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-primary);
}

.setting-item input[type="number"],
.setting-item input[type="range"],
.setting-item select {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-surface);
  color: var(--color-text-primary);
  font-family: var(--font-family);
  font-size: 0.875rem;
  transition: all var(--transition-fast);
}

.setting-item input[type="number"]:focus,
.setting-item select:focus {
  outline: none;
  border-color: var(--color-primary);
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1);
}

.setting-item input[type="range"] {
  padding: 0;
  height: 6px;
  background: var(--color-border);
  border: none;
  border-radius: var(--radius-full);
  appearance: none;
}

.setting-item input[type="range"]::-webkit-slider-thumb {
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  border: 2px solid var(--color-background);
  box-shadow: var(--shadow-sm);
}

.setting-item input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--color-primary);
  cursor: pointer;
  border: 2px solid var(--color-background);
  box-shadow: var(--shadow-sm);
}

.volume-value {
  display: inline-block;
  margin-top: var(--spacing-xs);
  font-size: 0.75rem;
  color: var(--color-text-secondary);
}

/* Checkbox Styling */
.checkbox-label {
  display: flex !important;
  align-items: center;
  gap: var(--spacing-sm);
  cursor: pointer;
  margin-bottom: 0 !important;
}

.checkbox-label input[type="checkbox"] {
  display: none;
}

.checkmark {
  width: 18px;
  height: 18px;
  border: 2px solid var(--color-border);
  border-radius: var(--radius-sm);
  background: var(--color-surface);
  position: relative;
  transition: all var(--transition-fast);
  flex-shrink: 0;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
  background: var(--color-primary);
  border-color: var(--color-primary);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
  content: '';
  position: absolute;
  left: 5px;
  top: 2px;
  width: 4px;
  height: 8px;
  border: solid white;
  border-width: 0 2px 2px 0;
  transform: rotate(45deg);
}

/* Action Buttons */
.setting-actions {
  display: flex;
  gap: var(--spacing-md);
  flex-wrap: wrap;
}

.action-btn {
  padding: var(--spacing-sm) var(--spacing-lg);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  background: var(--color-surface);
  color: var(--color-text-primary);
  font-family: var(--font-family);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  flex: 1;
  min-width: 120px;
}

.action-btn:hover {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
}

.action-btn.secondary {
  background: var(--color-surface);
  color: var(--color-text-secondary);
}

.action-btn.secondary:hover {
  color: var(--color-text-primary);
}

/* Notification styles */
.notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  max-width: 300px;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid var(--color-success);
}

.notification.warning {
  border-left: 4px solid var(--color-warning);
}

.notification.error {
  border-left: 4px solid var(--color-primary);
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .timer-controls {
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .control-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    min-width: 60px;
  }
  
  .btn-text {
    display: none;
  }
  
  .btn-icon {
    width: 20px;
    height: 20px;
  }
  
  .settings-panel {
    width: 100%;
    right: -100%;
  }
  
  .notification {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    max-width: none;
  }
}

/* Focus styles for accessibility */
.control-btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.settings-btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .control-btn {
    border-width: 2px;
  }

  .progress-ring-progress {
    stroke-width: 10;
  }
}

/* Break Confirmation Dialog */
.break-confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  backdrop-filter: blur(4px);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.break-confirmation-overlay.show {
  opacity: 1;
  visibility: visible;
}

.break-confirmation-dialog {
  background: var(--color-surface);
  border-radius: var(--radius-lg);
  padding: var(--spacing-xl);
  max-width: 480px;
  width: 90%;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: var(--shadow-xl);
  border: 1px solid var(--color-border);
  transform: scale(0.9) translateY(20px);
  transition: all 0.3s ease;
}

.break-confirmation-dialog.show {
  transform: scale(1) translateY(0);
}

.break-dialog-content {
  text-align: center;
}

.break-icon {
  margin-bottom: var(--spacing-lg);
  color: var(--color-success);
}

.break-icon svg {
  width: 64px;
  height: 64px;
}

.break-title {
  margin: 0 0 var(--spacing-lg) 0;
  color: var(--color-text-primary);
  font-size: 1.5rem;
  font-weight: var(--font-weight-bold);
}

.break-info {
  margin-bottom: var(--spacing-xl);
}

.break-message {
  font-size: 1.1rem;
  color: var(--color-text-primary);
  margin: 0 0 var(--spacing-sm) 0;
}

.break-type {
  color: var(--color-primary);
  font-weight: var(--font-weight-semibold);
}

.break-subtitle {
  font-size: 0.9rem;
  color: var(--color-text-secondary);
  margin: 0;
}

.break-stats {
  display: flex;
  justify-content: space-around;
  margin-bottom: var(--spacing-xl);
  padding: var(--spacing-md);
  background: var(--color-background);
  border-radius: var(--radius-md);
  border: 1px solid var(--color-border);
}

.break-stats .stat-item {
  background: none;
  border: none;
  padding: 0;
}

.break-stats .stat-label {
  display: block;
  font-size: 0.8rem;
  color: var(--color-text-secondary);
  margin-bottom: var(--spacing-xs);
}

.delay-time,
.bonus-time {
  font-size: 1.2rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
}

.bonus-time.has-bonus {
  color: var(--color-success);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { opacity: 1; }
  50% { opacity: 0.7; }
}

.break-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
}

.break-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-size: 1rem;
  font-weight: var(--font-weight-semibold);
  cursor: pointer;
  transition: all var(--transition-fast);
  min-width: 140px;
  justify-content: center;
}

.break-btn.primary {
  background: var(--color-primary);
  color: white;
}

.break-btn.primary:hover {
  background: var(--color-primary-hover);
  transform: translateY(-1px);
}

.break-btn.secondary {
  background: var(--color-surface);
  color: var(--color-text-primary);
  border: 2px solid var(--color-border);
}

.break-btn.secondary:hover {
  background: var(--color-background);
  border-color: var(--color-primary);
  color: var(--color-primary);
}

.break-btn:focus {
  outline: none;
  box-shadow: 0 0 0 3px var(--color-primary-alpha);
}

.break-btn svg {
  width: 20px;
  height: 20px;
}

/* Break Controls in Timer Controls */
.break-controls {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  width: 100%;
}

.break-controls .control-btn {
  flex: 1;
  max-width: 160px;
}

.break-controls .control-btn.primary {
  background: var(--color-success);
  color: white;
  border-color: var(--color-success);
}

.break-controls .control-btn.primary:hover {
  background: var(--color-success-hover);
  border-color: var(--color-success-hover);
}

.break-controls .control-btn.secondary {
  background: var(--color-surface);
  color: var(--color-text-secondary);
  border-color: var(--color-border);
}

.break-controls .control-btn.secondary:hover {
  background: var(--color-background);
  color: var(--color-text-primary);
  border-color: var(--color-primary);
}
