/**
 * Component Styles
 */

/* Timer Controls */
.timer-controls-container {
  display: flex;
  justify-content: center;
}

.timer-controls {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  padding: var(--spacing-md);
  background: var(--color-background);
  border-radius: var(--radius-lg);
  border: 1px solid var(--color-border);
  box-shadow: var(--shadow-sm);
}

.control-btn {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
  padding: var(--spacing-md) var(--spacing-lg);
  border: none;
  border-radius: var(--radius-md);
  font-family: var(--font-family);
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
  cursor: pointer;
  transition: all var(--transition-fast);
  background: var(--color-surface);
  color: var(--color-text-primary);
  border: 1px solid var(--color-border);
  min-width: 80px;
  justify-content: center;
}

.control-btn:hover:not(:disabled) {
  background: var(--color-surface-hover);
  border-color: var(--color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--shadow-md);
}

.control-btn:active {
  transform: translateY(0);
  box-shadow: var(--shadow-sm);
}

.control-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.control-btn.clicked {
  transform: scale(0.95);
}

/* Specific button styles */
.start-btn,
.pause-btn {
  background: var(--color-primary);
  color: white;
  border-color: var(--color-primary);
}

.start-btn:hover:not(:disabled),
.pause-btn:hover:not(:disabled) {
  background: var(--color-primary-hover);
  border-color: var(--color-primary-hover);
}

.reset-btn {
  background: var(--color-surface);
  color: var(--color-text-secondary);
}

.reset-btn:hover:not(:disabled) {
  color: var(--color-text-primary);
}

.skip-btn {
  background: var(--color-warning);
  color: white;
  border-color: var(--color-warning);
}

.skip-btn:hover:not(:disabled) {
  background: #b45309;
  border-color: #b45309;
}

/* Button icons */
.btn-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.btn-text {
  font-size: 0.875rem;
  font-weight: var(--font-weight-medium);
}

/* Timer states */
.timer-controls.timer-running .start-btn {
  display: none;
}

.timer-controls.timer-paused .pause-btn {
  display: none;
}

.timer-controls.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* Settings Panel (for future implementation) */
.settings-panel {
  position: fixed;
  top: 0;
  right: -400px;
  width: 400px;
  height: 100vh;
  background: var(--color-background);
  border-left: 1px solid var(--color-border);
  box-shadow: var(--shadow-lg);
  transition: right var(--transition-normal);
  z-index: 1000;
  overflow-y: auto;
}

.settings-panel.open {
  right: 0;
}

.settings-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
  opacity: 0;
  visibility: hidden;
  transition: all var(--transition-normal);
  z-index: 999;
}

.settings-overlay.open {
  opacity: 1;
  visibility: visible;
}

/* Notification styles */
.notification {
  position: fixed;
  top: var(--spacing-lg);
  right: var(--spacing-lg);
  background: var(--color-background);
  border: 1px solid var(--color-border);
  border-radius: var(--radius-md);
  padding: var(--spacing-md) var(--spacing-lg);
  box-shadow: var(--shadow-lg);
  max-width: 300px;
  z-index: 1001;
  transform: translateX(100%);
  transition: transform var(--transition-normal);
}

.notification.show {
  transform: translateX(0);
}

.notification.success {
  border-left: 4px solid var(--color-success);
}

.notification.warning {
  border-left: 4px solid var(--color-warning);
}

.notification.error {
  border-left: 4px solid var(--color-primary);
}

/* Loading states */
.loading {
  opacity: 0.6;
  pointer-events: none;
}

.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 20px;
  height: 20px;
  margin: -10px 0 0 -10px;
  border: 2px solid var(--color-border);
  border-top-color: var(--color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .timer-controls {
    gap: var(--spacing-sm);
    padding: var(--spacing-sm);
  }
  
  .control-btn {
    padding: var(--spacing-sm) var(--spacing-md);
    min-width: 60px;
  }
  
  .btn-text {
    display: none;
  }
  
  .btn-icon {
    width: 20px;
    height: 20px;
  }
  
  .settings-panel {
    width: 100%;
    right: -100%;
  }
  
  .notification {
    top: var(--spacing-sm);
    right: var(--spacing-sm);
    left: var(--spacing-sm);
    max-width: none;
  }
}

/* Focus styles for accessibility */
.control-btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

.settings-btn:focus-visible {
  outline: 2px solid var(--color-primary);
  outline-offset: 2px;
}

/* High contrast mode support */
@media (prefers-contrast: high) {
  .control-btn {
    border-width: 2px;
  }
  
  .progress-ring-progress {
    stroke-width: 10;
  }
}
