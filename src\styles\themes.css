/**
 * Theme Styles and Animations
 */

/* Theme transition animations */
* {
  transition: background-color var(--transition-normal), 
              color var(--transition-normal), 
              border-color var(--transition-normal);
}

/* Auto theme detection */
@media (prefers-color-scheme: dark) {
  :root:not([data-theme="light"]) {
    --color-primary: #ef4444;
    --color-primary-hover: #dc2626;
    --color-primary-light: #1f1f1f;
    
    --color-success: #22c55e;
    --color-success-light: #0f1f0f;
    
    --color-warning: #f59e0b;
    --color-warning-light: #1f1f0f;
    
    --color-background: #0f172a;
    --color-surface: #1e293b;
    --color-surface-hover: #334155;
    
    --color-text-primary: #f8fafc;
    --color-text-secondary: #cbd5e1;
    --color-text-muted: #64748b;
    
    --color-border: #334155;
    --color-border-hover: #475569;
    
    --color-shadow: rgb(0 0 0 / 0.3);
  }
}

/* Theme-specific animations */
.theme-transition {
  transition: all var(--transition-slow) ease-in-out;
}

/* Session type themes */
.session-work {
  --session-color: var(--color-primary);
  --session-color-light: var(--color-primary-light);
}

.session-shortBreak {
  --session-color: var(--color-success);
  --session-color-light: var(--color-success-light);
}

.session-longBreak {
  --session-color: var(--color-warning);
  --session-color-light: var(--color-warning-light);
}

/* Session-specific styling */
.session-work .timer-time {
  color: var(--color-primary);
}

.session-shortBreak .timer-time {
  color: var(--color-success);
}

.session-longBreak .timer-time {
  color: var(--color-warning);
}

.session-work .session-type {
  color: var(--color-primary);
}

.session-shortBreak .session-type {
  color: var(--color-success);
}

.session-longBreak .session-type {
  color: var(--color-warning);
}

/* Minimalist mode */
.minimalist-mode .app-header,
.minimalist-mode .app-footer {
  display: none;
}

.minimalist-mode .session-info,
.minimalist-mode .session-progress {
  display: none;
}

.minimalist-mode .timer-label {
  display: none;
}

.minimalist-mode .pomodoro-app {
  background: transparent;
  box-shadow: none;
  border: none;
}

.minimalist-mode .app-main {
  padding: var(--spacing-md);
}

/* Focus mode animations */
.focus-mode {
  animation: focus-breathe 4s ease-in-out infinite;
}

@keyframes focus-breathe {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.02);
    opacity: 0.95;
  }
}

/* Break mode animations */
.break-mode {
  animation: break-pulse 2s ease-in-out infinite;
}

@keyframes break-pulse {
  0%, 100% {
    filter: brightness(1);
  }
  50% {
    filter: brightness(1.1);
  }
}

/* Completion animations */
.completion-animation {
  animation: completion-celebration 2s ease-in-out;
}

@keyframes completion-celebration {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1) rotate(2deg);
  }
  50% {
    transform: scale(1.05) rotate(-1deg);
  }
  75% {
    transform: scale(1.08) rotate(1deg);
  }
  100% {
    transform: scale(1);
  }
}

/* Progress ring animations */
.progress-ring-progress {
  animation: progress-glow 2s ease-in-out infinite alternate;
}

@keyframes progress-glow {
  from {
    filter: drop-shadow(0 0 5px currentColor);
  }
  to {
    filter: drop-shadow(0 0 10px currentColor);
  }
}

/* Reduced motion preferences */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
  
  .focus-mode,
  .break-mode,
  .completion-animation,
  .progress-ring-progress {
    animation: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  :root {
    --color-primary: #ff0000;
    --color-success: #00ff00;
    --color-warning: #ffff00;
    --color-background: #ffffff;
    --color-surface: #f0f0f0;
    --color-text-primary: #000000;
    --color-text-secondary: #333333;
    --color-border: #000000;
  }
  
  [data-theme="dark"] {
    --color-primary: #ff6666;
    --color-success: #66ff66;
    --color-warning: #ffff66;
    --color-background: #000000;
    --color-surface: #1a1a1a;
    --color-text-primary: #ffffff;
    --color-text-secondary: #cccccc;
    --color-border: #ffffff;
  }
}

/* Print styles */
@media print {
  .pomodoro-app {
    background: white !important;
    color: black !important;
    box-shadow: none !important;
  }
  
  .timer-controls,
  .settings-btn {
    display: none !important;
  }
  
  .progress-ring-progress {
    stroke: black !important;
  }
}

/* Custom scrollbar for webkit browsers */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--color-surface);
}

::-webkit-scrollbar-thumb {
  background: var(--color-border);
  border-radius: var(--radius-sm);
}

::-webkit-scrollbar-thumb:hover {
  background: var(--color-border-hover);
}
