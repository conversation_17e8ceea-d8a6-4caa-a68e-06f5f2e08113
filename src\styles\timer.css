/**
 * Timer Display Styles
 */

/* Timer display container */
.timer-display-container {
  display: flex;
  justify-content: center;
  margin-bottom: var(--spacing-xl);
}

.timer-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-lg);
  width: 100%;
  max-width: 320px;
}

/* Session info */
.session-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
  margin-bottom: var(--spacing-md);
}

.session-type {
  font-size: 1.125rem;
  font-weight: var(--font-weight-semibold);
  color: var(--color-text-primary);
}

.session-number {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  background: var(--color-surface);
  padding: var(--spacing-xs) var(--spacing-sm);
  border-radius: var(--radius-full);
  border: 1px solid var(--color-border);
}

/* Timer circle */
.timer-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300px;
  height: 300px;
}

.progress-ring {
  position: absolute;
  top: 0;
  left: 0;
  transform: rotate(-90deg);
  width: 100%;
  height: 100%;
}

.progress-ring-background {
  stroke: var(--color-border);
  opacity: 0.3;
}

.progress-ring-progress {
  stroke: var(--color-primary);
  stroke-linecap: round;
  transition: stroke-dashoffset var(--transition-normal);
}

/* Session type specific colors */
.session-work .progress-ring-progress {
  stroke: var(--color-primary);
}

.session-shortBreak .progress-ring-progress {
  stroke: var(--color-success);
}

.session-longBreak .progress-ring-progress {
  stroke: var(--color-warning);
}

/* Timer content */
.timer-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: var(--spacing-sm);
  z-index: 1;
}

.timer-time {
  font-size: 3.5rem;
  font-weight: var(--font-weight-bold);
  color: var(--color-text-primary);
  font-variant-numeric: tabular-nums;
  line-height: 1;
}

.timer-label {
  font-size: 1rem;
  font-weight: var(--font-weight-medium);
  color: var(--color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Session progress dots */
.session-progress {
  display: flex;
  justify-content: center;
  margin-top: var(--spacing-md);
}

.progress-dots {
  display: flex;
  gap: var(--spacing-sm);
}

.dot {
  width: 12px;
  height: 12px;
  border-radius: var(--radius-full);
  background: var(--color-border);
  transition: all var(--transition-fast);
}

.dot.active {
  background: var(--color-primary);
  transform: scale(1.2);
}

.dot.completed {
  background: var(--color-success);
}

/* Timer states */
.timer-running .timer-time {
  color: var(--color-primary);
}

.timer-paused .timer-time {
  color: var(--color-warning);
}

.timer-complete {
  animation: completion-pulse 1s ease-in-out;
}

@keyframes completion-pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

/* Responsive design */
@media (max-width: 480px) {
  .timer-circle {
    width: 250px;
    height: 250px;
  }
  
  .timer-time {
    font-size: 2.5rem;
  }
  
  .session-info {
    flex-direction: column;
    gap: var(--spacing-sm);
    text-align: center;
  }
}

@media (max-width: 320px) {
  .timer-circle {
    width: 200px;
    height: 200px;
  }
  
  .timer-time {
    font-size: 2rem;
  }
}
