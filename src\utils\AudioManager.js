/**
 * AudioManager Class - Handles audio notifications and sound effects
 */
export class AudioManager {
  constructor() {
    this.audioContext = null;
    this.sounds = new Map();
    this.volume = 0.5;
    this.enabled = true;
    
    this.initializeAudioContext();
    this.createDefaultSounds();
  }

  /**
   * Initialize Web Audio API context
   */
  async initializeAudioContext() {
    try {
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      
      // Resume context on user interaction (required by browsers)
      if (this.audioContext.state === 'suspended') {
        document.addEventListener('click', () => {
          this.audioContext.resume();
        }, { once: true });
      }
    } catch (error) {
      console.warn('Web Audio API not supported:', error);
    }
  }

  /**
   * Create default notification sounds using oscillators
   */
  createDefaultSounds() {
    // Work session complete sound (gentle chime)
    this.sounds.set('workComplete', {
      type: 'generated',
      generator: () => this.createChimeSound([523.25, 659.25, 783.99], 0.8) // C5, E5, G5
    });

    // Break complete sound (upbeat notification)
    this.sounds.set('breakComplete', {
      type: 'generated',
      generator: () => this.createChimeSound([440, 554.37, 659.25], 0.6) // A4, C#5, E5
    });

    // Tick sound (subtle click)
    this.sounds.set('tick', {
      type: 'generated',
      generator: () => this.createTickSound()
    });

    // Button click sound
    this.sounds.set('click', {
      type: 'generated',
      generator: () => this.createClickSound()
    });
  }

  /**
   * Create chime sound with multiple frequencies
   */
  createChimeSound(frequencies, duration = 1.0) {
    if (!this.audioContext) return null;

    const gainNode = this.audioContext.createGain();
    gainNode.connect(this.audioContext.destination);
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(this.volume * 0.3, this.audioContext.currentTime + 0.1);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);

    frequencies.forEach((freq, index) => {
      const oscillator = this.audioContext.createOscillator();
      oscillator.type = 'sine';
      oscillator.frequency.setValueAtTime(freq, this.audioContext.currentTime);
      
      // Add slight delay between notes for harmony
      const delay = index * 0.1;
      oscillator.connect(gainNode);
      oscillator.start(this.audioContext.currentTime + delay);
      oscillator.stop(this.audioContext.currentTime + duration + delay);
    });

    return { duration: duration + 0.3 };
  }

  /**
   * Create subtle tick sound
   */
  createTickSound() {
    if (!this.audioContext) return null;

    const gainNode = this.audioContext.createGain();
    gainNode.connect(this.audioContext.destination);
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(this.volume * 0.1, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.1);

    const oscillator = this.audioContext.createOscillator();
    oscillator.type = 'square';
    oscillator.frequency.setValueAtTime(800, this.audioContext.currentTime);
    oscillator.connect(gainNode);
    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + 0.1);

    return { duration: 0.1 };
  }

  /**
   * Create button click sound
   */
  createClickSound() {
    if (!this.audioContext) return null;

    const gainNode = this.audioContext.createGain();
    gainNode.connect(this.audioContext.destination);
    gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
    gainNode.gain.linearRampToValueAtTime(this.volume * 0.2, this.audioContext.currentTime + 0.01);
    gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + 0.15);

    const oscillator = this.audioContext.createOscillator();
    oscillator.type = 'triangle';
    oscillator.frequency.setValueAtTime(1000, this.audioContext.currentTime);
    oscillator.frequency.exponentialRampToValueAtTime(500, this.audioContext.currentTime + 0.15);
    oscillator.connect(gainNode);
    oscillator.start(this.audioContext.currentTime);
    oscillator.stop(this.audioContext.currentTime + 0.15);

    return { duration: 0.15 };
  }

  /**
   * Play a sound by name
   */
  async play(soundName) {
    if (!this.enabled || !this.audioContext) return;

    const sound = this.sounds.get(soundName);
    if (!sound) {
      console.warn(`Sound "${soundName}" not found`);
      return;
    }

    try {
      if (sound.type === 'generated') {
        sound.generator();
      } else if (sound.type === 'file') {
        await this.playAudioFile(sound.url);
      }
    } catch (error) {
      console.warn(`Failed to play sound "${soundName}":`, error);
    }
  }

  /**
   * Play audio file (for custom sounds)
   */
  async playAudioFile(url) {
    if (!this.audioContext) return;

    try {
      const response = await fetch(url);
      const arrayBuffer = await response.arrayBuffer();
      const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);

      const source = this.audioContext.createBufferSource();
      const gainNode = this.audioContext.createGain();
      
      source.buffer = audioBuffer;
      source.connect(gainNode);
      gainNode.connect(this.audioContext.destination);
      gainNode.gain.setValueAtTime(this.volume, this.audioContext.currentTime);
      
      source.start();
    } catch (error) {
      console.warn('Failed to play audio file:', error);
    }
  }

  /**
   * Add custom sound from file
   */
  addSound(name, url) {
    this.sounds.set(name, {
      type: 'file',
      url: url
    });
  }

  /**
   * Set volume (0.0 to 1.0)
   */
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
  }

  /**
   * Get current volume
   */
  getVolume() {
    return this.volume;
  }

  /**
   * Enable/disable audio
   */
  setEnabled(enabled) {
    this.enabled = enabled;
  }

  /**
   * Check if audio is enabled
   */
  isEnabled() {
    return this.enabled;
  }

  /**
   * Test audio system
   */
  test() {
    this.play('click');
  }

  /**
   * Get available sounds
   */
  getAvailableSounds() {
    return Array.from(this.sounds.keys());
  }

  /**
   * Cleanup resources
   */
  destroy() {
    if (this.audioContext && this.audioContext.state !== 'closed') {
      this.audioContext.close();
    }
    this.sounds.clear();
  }
}

// Create global instance
export const audioManager = new AudioManager();
