/**
 * EventEmitter Class - Custom event system for application-wide communication
 * Provides a centralized way to handle events across components
 */
export class EventEmitter {
  constructor() {
    this.events = new Map();
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (!this.events.has(event)) {
      this.events.set(event, []);
    }
    this.events.get(event).push(callback);
    
    // Return unsubscribe function
    return () => this.off(event, callback);
  }

  /**
   * Add one-time event listener
   */
  once(event, callback) {
    const onceCallback = (...args) => {
      callback(...args);
      this.off(event, onceCallback);
    };
    return this.on(event, onceCallback);
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (!this.events.has(event)) return;
    
    const callbacks = this.events.get(event);
    const index = callbacks.indexOf(callback);
    
    if (index > -1) {
      callbacks.splice(index, 1);
    }
    
    // Clean up empty event arrays
    if (callbacks.length === 0) {
      this.events.delete(event);
    }
  }

  /**
   * Emit event to all listeners
   */
  emit(event, ...args) {
    if (!this.events.has(event)) return;
    
    const callbacks = this.events.get(event).slice(); // Create copy to avoid issues with modifications during iteration
    callbacks.forEach(callback => {
      try {
        callback(...args);
      } catch (error) {
        console.error(`Error in event listener for "${event}":`, error);
      }
    });
  }

  /**
   * Remove all listeners for an event
   */
  removeAllListeners(event) {
    if (event) {
      this.events.delete(event);
    } else {
      this.events.clear();
    }
  }

  /**
   * Get list of events
   */
  eventNames() {
    return Array.from(this.events.keys());
  }

  /**
   * Get listener count for an event
   */
  listenerCount(event) {
    return this.events.has(event) ? this.events.get(event).length : 0;
  }
}

/**
 * NotificationManager Class - Handles in-app notifications
 */
export class NotificationManager {
  constructor() {
    this.container = null;
    this.notifications = new Map();
    this.nextId = 1;
    this.defaultDuration = 4000; // 4 seconds
    
    this.createContainer();
  }

  /**
   * Create notification container
   */
  createContainer() {
    this.container = document.createElement('div');
    this.container.className = 'notification-container';
    this.container.style.cssText = `
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      pointer-events: none;
    `;
    document.body.appendChild(this.container);
  }

  /**
   * Show notification
   */
  show(message, type = 'info', duration = this.defaultDuration) {
    const id = this.nextId++;
    const notification = this.createNotification(id, message, type);
    
    this.container.appendChild(notification);
    this.notifications.set(id, notification);
    
    // Trigger animation
    requestAnimationFrame(() => {
      notification.classList.add('show');
    });
    
    // Auto-remove after duration
    if (duration > 0) {
      setTimeout(() => {
        this.remove(id);
      }, duration);
    }
    
    return id;
  }

  /**
   * Create notification element
   */
  createNotification(id, message, type) {
    const notification = document.createElement('div');
    notification.className = `notification ${type}`;
    notification.style.cssText = `
      background: var(--color-background);
      border: 1px solid var(--color-border);
      border-radius: var(--radius-md);
      padding: var(--spacing-md) var(--spacing-lg);
      margin-bottom: var(--spacing-sm);
      box-shadow: var(--shadow-lg);
      max-width: 300px;
      transform: translateX(100%);
      transition: transform var(--transition-normal);
      pointer-events: auto;
      cursor: pointer;
    `;
    
    // Add type-specific styling
    if (type === 'success') {
      notification.style.borderLeftColor = 'var(--color-success)';
      notification.style.borderLeftWidth = '4px';
    } else if (type === 'warning') {
      notification.style.borderLeftColor = 'var(--color-warning)';
      notification.style.borderLeftWidth = '4px';
    } else if (type === 'error') {
      notification.style.borderLeftColor = 'var(--color-primary)';
      notification.style.borderLeftWidth = '4px';
    }
    
    notification.innerHTML = `
      <div style="display: flex; align-items: flex-start; gap: var(--spacing-sm);">
        <div style="flex: 1; font-size: 0.875rem; color: var(--color-text-primary);">
          ${message}
        </div>
        <button style="
          background: none;
          border: none;
          color: var(--color-text-secondary);
          cursor: pointer;
          padding: 0;
          font-size: 1.2rem;
          line-height: 1;
        ">&times;</button>
      </div>
    `;
    
    // Add click to dismiss
    notification.addEventListener('click', () => {
      this.remove(id);
    });
    
    return notification;
  }

  /**
   * Remove notification
   */
  remove(id) {
    const notification = this.notifications.get(id);
    if (!notification) return;
    
    notification.classList.remove('show');
    notification.style.transform = 'translateX(100%)';
    
    setTimeout(() => {
      if (notification.parentNode) {
        notification.parentNode.removeChild(notification);
      }
      this.notifications.delete(id);
    }, 250);
  }

  /**
   * Clear all notifications
   */
  clear() {
    this.notifications.forEach((_, id) => {
      this.remove(id);
    });
  }

  /**
   * Show success notification
   */
  success(message, duration) {
    return this.show(message, 'success', duration);
  }

  /**
   * Show warning notification
   */
  warning(message, duration) {
    return this.show(message, 'warning', duration);
  }

  /**
   * Show error notification
   */
  error(message, duration) {
    return this.show(message, 'error', duration);
  }

  /**
   * Show info notification
   */
  info(message, duration) {
    return this.show(message, 'info', duration);
  }
}

// Create global instances
export const globalEventEmitter = new EventEmitter();
export const notificationManager = new NotificationManager();
