/**
 * MusicPlayer - Handles background music during focus sessions
 */
export class MusicPlayer {
  constructor() {
    this.audio = null;
    this.currentTrack = null;
    this.musicList = [];
    this.isPlaying = false;
    this.volume = 0.3;
    this.isEnabled = false;
    
    this.loadMusicList();
  }

  /**
   * Load available music list
   */
  async loadMusicList() {
    try {
      const response = await fetch('/music/music-list.json');
      this.musicList = await response.json();
      console.log('Music list loaded:', this.musicList.length, 'tracks');
    } catch (error) {
      console.warn('Could not load music list:', error);
      this.musicList = [];
    }
  }

  /**
   * Get available music tracks
   */
  getMusicList() {
    return this.musicList;
  }

  /**
   * Set current track
   */
  setTrack(trackId) {
    const track = this.musicList.find(t => t.id === trackId);
    if (!track) {
      console.warn('Track not found:', trackId);
      return false;
    }

    this.currentTrack = track;
    
    // Stop current audio if playing
    if (this.audio) {
      this.audio.pause();
      this.audio = null;
    }

    // Create new audio element
    this.audio = new Audio(`/music/${track.filename}`);
    this.audio.loop = true;
    this.audio.volume = this.volume;
    
    // Handle audio events
    this.audio.addEventListener('error', (e) => {
      console.warn('Audio error for track:', track.name, e);
    });

    this.audio.addEventListener('canplaythrough', () => {
      console.log('Audio ready:', track.name);
    });

    return true;
  }

  /**
   * Play current track
   */
  async play() {
    if (!this.isEnabled || !this.audio || !this.currentTrack) {
      return false;
    }

    try {
      await this.audio.play();
      this.isPlaying = true;
      console.log('Playing:', this.currentTrack.name);
      return true;
    } catch (error) {
      console.warn('Could not play audio:', error);
      return false;
    }
  }

  /**
   * Pause current track
   */
  pause() {
    if (this.audio && this.isPlaying) {
      this.audio.pause();
      this.isPlaying = false;
      console.log('Paused:', this.currentTrack?.name);
    }
  }

  /**
   * Stop current track
   */
  stop() {
    if (this.audio) {
      this.audio.pause();
      this.audio.currentTime = 0;
      this.isPlaying = false;
      console.log('Stopped:', this.currentTrack?.name);
    }
  }

  /**
   * Set volume (0-1)
   */
  setVolume(volume) {
    this.volume = Math.max(0, Math.min(1, volume));
    if (this.audio) {
      this.audio.volume = this.volume;
    }
  }

  /**
   * Get current volume
   */
  getVolume() {
    return this.volume;
  }

  /**
   * Enable/disable music player
   */
  setEnabled(enabled) {
    this.isEnabled = enabled;
    if (!enabled && this.isPlaying) {
      this.pause();
    }
  }

  /**
   * Check if music player is enabled
   */
  isPlayerEnabled() {
    return this.isEnabled;
  }

  /**
   * Get current track info
   */
  getCurrentTrack() {
    return this.currentTrack;
  }

  /**
   * Check if currently playing
   */
  isCurrentlyPlaying() {
    return this.isPlaying;
  }

  /**
   * Start music for focus session
   */
  startFocusMusic() {
    if (this.isEnabled && this.currentTrack) {
      this.play();
    }
  }

  /**
   * Stop music for break/pause
   */
  stopFocusMusic() {
    this.pause();
  }

  /**
   * Get player state
   */
  getState() {
    return {
      isEnabled: this.isEnabled,
      isPlaying: this.isPlaying,
      currentTrack: this.currentTrack,
      volume: this.volume,
      musicList: this.musicList
    };
  }

  /**
   * Cleanup
   */
  destroy() {
    this.stop();
    if (this.audio) {
      this.audio = null;
    }
  }
}

// Create global music player instance
export const musicPlayer = new MusicPlayer();
