/**
 * NotificationManager - Handles browser notifications and sounds
 */
export class NotificationManager {
  constructor() {
    this.isSupported = 'Notification' in window;
    this.permission = this.isSupported ? Notification.permission : 'denied';
    this.audioContext = null;
    
    console.log('🔔 NotificationManager initialized', {
      supported: this.isSupported,
      permission: this.permission
    });
  }

  /**
   * Request notification permission
   */
  async requestPermission() {
    if (!this.isSupported) {
      console.warn('🔔 Browser notifications not supported');
      return false;
    }

    if (this.permission === 'granted') {
      return true;
    }

    try {
      this.permission = await Notification.requestPermission();
      console.log('🔔 Notification permission:', this.permission);
      return this.permission === 'granted';
    } catch (error) {
      console.warn('🔔 Failed to request notification permission:', error);
      return false;
    }
  }

  /**
   * Show browser notification
   */
  showNotification(title, options = {}) {
    if (!this.isSupported || this.permission !== 'granted') {
      console.log('🔔 Notification skipped - not supported or permission denied');
      return null;
    }

    try {
      const notification = new Notification(title, {
        icon: '/vite.svg',
        badge: '/vite.svg',
        ...options
      });

      console.log('🔔 Notification shown:', title);

      // Auto close after 5 seconds
      setTimeout(() => {
        notification.close();
      }, 5000);

      return notification;
    } catch (error) {
      console.warn('🔔 Failed to show notification:', error);
      return null;
    }
  }

  /**
   * Play notification sound using Web Audio API
   */
  playNotificationSound(frequency = 800, duration = 200, volume = 0.3) {
    try {
      // Create audio context if not exists
      if (!this.audioContext) {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
      }

      // Create oscillator for beep sound
      const oscillator = this.audioContext.createOscillator();
      const gainNode = this.audioContext.createGain();

      // Connect nodes
      oscillator.connect(gainNode);
      gainNode.connect(this.audioContext.destination);

      // Configure sound
      oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
      oscillator.type = 'sine';

      // Configure volume envelope
      gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
      gainNode.gain.linearRampToValueAtTime(volume, this.audioContext.currentTime + 0.01);
      gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration / 1000);

      // Play sound
      oscillator.start(this.audioContext.currentTime);
      oscillator.stop(this.audioContext.currentTime + duration / 1000);

      console.log('🔊 Notification sound played');
    } catch (error) {
      console.warn('🔊 Failed to play notification sound:', error);
    }
  }

  /**
   * Show session complete notification
   */
  showSessionComplete(sessionType, nextSessionType) {
    const messages = {
      work: {
        title: '🎉 Work Session Complete!',
        body: 'Great job! Time for a well-deserved break.',
        icon: '🎉'
      },
      shortBreak: {
        title: '⏰ Break Time Over!',
        body: 'Ready to get back to work?',
        icon: '⏰'
      },
      longBreak: {
        title: '🌟 Long Break Complete!',
        body: 'Refreshed and ready for the next work session!',
        icon: '🌟'
      }
    };

    const message = messages[sessionType] || messages.work;
    
    console.log(`🔔 Session complete notification: ${sessionType} → ${nextSessionType}`);
    
    this.showNotification(message.title, {
      body: message.body,
      tag: 'pomodoro-session-complete'
    });
  }

  /**
   * Show break confirmation notification
   */
  showBreakPending(breakType, bonusTime = 0) {
    const title = breakType === 'longBreak' ? '🌟 Long Break Ready!' : '☕ Break Time!';
    const bonusText = bonusTime > 0 ? ` (+${Math.floor(bonusTime / 60)}:${(bonusTime % 60).toString().padStart(2, '0')} bonus)` : '';
    const body = `Your break is ready${bonusText}. Take your time!`;
    
    console.log(`🔔 Break pending notification: ${breakType}${bonusText}`);
    
    this.showNotification(title, {
      body: body,
      tag: 'pomodoro-break-pending',
      requireInteraction: true
    });
  }

  /**
   * Get notification state
   */
  getState() {
    return {
      supported: this.isSupported,
      permission: this.permission,
      audioContextState: this.audioContext?.state || 'none'
    };
  }

  /**
   * Cleanup
   */
  destroy() {
    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }
    console.log('🔔 NotificationManager destroyed');
  }
}
