/**
 * PomodoroApp Class - Main application state manager
 * Coordinates Timer, Session, and Settings classes
 * Manages overall application state and component communication
 */
import { Timer } from './Timer.js';
import { Session } from './Session.js';
import { Settings } from './Settings.js';
import { audioManager } from './AudioManager.js';
import { notificationManager } from './EventEmitter.js';

export class PomodoroApp {
  constructor() {
    // Initialize core components
    this.settings = new Settings();
    this.session = new Session(this.settings.getTimerSettings());
    this.timer = new Timer();
    
    // Application state
    this.isInitialized = false;
    this.components = new Map(); // Store UI components
    
    // Bind event handlers
    this.setupEventHandlers();
    
    // Initialize timer with current session
    this.updateTimerForCurrentSession();
  }

  /**
   * Initialize the application
   */
  init() {
    if (this.isInitialized) return;
    
    // Apply initial theme
    this.settings.applyTheme();
    
    // Request notification permission if needed
    this.requestNotificationPermission();
    
    this.isInitialized = true;
    console.log('Pomodoro App initialized');
  }

  /**
   * Setup event handlers between components
   */
  setupEventHandlers() {
    // Timer events
    this.timer.on('onTick', (timeRemaining) => {
      this.notifyComponents('timerTick', { timeRemaining, timer: this.timer.getState() });
    });

    this.timer.on('onComplete', () => {
      this.handleTimerComplete();
    });

    this.timer.on('onStart', () => {
      this.notifyComponents('timerStart', this.timer.getState());
    });

    this.timer.on('onPause', () => {
      this.notifyComponents('timerPause', this.timer.getState());
    });

    this.timer.on('onReset', () => {
      this.notifyComponents('timerReset', this.timer.getState());
    });

    // Session events
    this.session.on('onSessionChange', (sessionData) => {
      this.updateTimerForCurrentSession();
      this.notifyComponents('sessionChange', sessionData);
    });

    this.session.on('onSessionComplete', (sessionData) => {
      this.notifyComponents('sessionComplete', sessionData);
      this.showNotification(sessionData);
      this.playNotificationSound();
    });

    this.session.on('onBreakPending', (breakData) => {
      this.notifyComponents('breakPending', breakData);
      this.showBreakConfirmation(breakData);
    });

    this.session.on('onBonusTimeAdded', (bonusData) => {
      this.notifyComponents('bonusTimeAdded', bonusData);
      notificationManager.success(bonusData.message, 3000);
    });

    this.session.on('onCycleComplete', (cycleData) => {
      this.notifyComponents('cycleComplete', cycleData);
    });

    // Settings events
    this.settings.on('onSettingsChange', (changes) => {
      this.handleSettingsChange(changes);
    });

    this.settings.on('onThemeChange', (theme) => {
      this.settings.applyTheme();
      this.notifyComponents('themeChange', theme);
    });
  }

  /**
   * Handle timer completion
   */
  handleTimerComplete() {
    const currentSession = this.session.getCurrentSession();
    
    // Complete the session
    this.session.completeSession();
    
    // Play notification sound
    if (this.settings.get('playSound')) {
      this.playNotificationSound();
    }
    
    // Auto-start next session if enabled
    if (this.session.shouldAutoStart()) {
      setTimeout(() => {
        this.startTimer();
      }, 1000); // 1 second delay
    }
    
    this.notifyComponents('timerComplete', currentSession);
  }

  /**
   * Handle settings changes
   */
  handleSettingsChange(changes) {
    // Update session settings if timer-related settings changed
    const timerSettings = ['workDuration', 'shortBreakDuration', 'longBreakDuration', 
                          'sessionsUntilLongBreak', 'autoStartBreaks', 'autoStartWork'];
    
    const hasTimerChanges = Object.keys(changes).some(key => timerSettings.includes(key));
    
    if (hasTimerChanges) {
      this.session.updateSettings(this.settings.getTimerSettings());
      this.updateTimerForCurrentSession();
    }
    
    this.notifyComponents('settingsChange', changes);
  }

  /**
   * Update timer duration based on current session
   */
  updateTimerForCurrentSession() {
    const currentSession = this.session.getCurrentSession();
    this.timer.setDuration(currentSession.duration);

    // If timer is not running, reset timeRemaining to new duration
    if (!this.timer.isRunning) {
      this.timer.timeRemaining = currentSession.duration;
    }
  }

  /**
   * Start the timer
   */
  startTimer() {
    this.timer.start();
  }

  /**
   * Pause the timer
   */
  pauseTimer() {
    this.timer.pause();
  }

  /**
   * Reset the timer
   */
  resetTimer() {
    this.timer.reset();
    // Ensure timer is reset to current session duration
    this.updateTimerForCurrentSession();
    this.notifyComponents('timerReset', this.timer.getState());
  }

  /**
   * Skip to next session
   */
  skipSession() {
    // Force complete current timer (this will trigger session completion)
    this.timer.skip();
  }

  /**
   * Confirm and start pending break
   */
  confirmBreak() {
    if (this.session.confirmBreak()) {
      this.updateTimerForCurrentSession();
      this.notifyComponents('breakConfirmed', this.getState());
      return true;
    }
    return false;
  }

  /**
   * Skip pending break and go to work
   */
  skipBreak() {
    if (this.session.skipBreak()) {
      this.updateTimerForCurrentSession();
      this.notifyComponents('breakSkipped', this.getState());
      return true;
    }
    return false;
  }

  /**
   * Get current application state
   */
  getState() {
    return {
      timer: this.timer.getState(),
      session: this.session.getCurrentSession(),
      stats: this.session.getStats(),
      settings: this.settings.getAll()
    };
  }

  /**
   * Register a UI component
   */
  registerComponent(name, component) {
    this.components.set(name, component);
  }

  /**
   * Unregister a UI component
   */
  unregisterComponent(name) {
    this.components.delete(name);
  }

  /**
   * Notify all registered components of state changes
   */
  notifyComponents(event, data) {
    this.components.forEach((component) => {
      if (typeof component.handleAppEvent === 'function') {
        component.handleAppEvent(event, data);
      }
    });
  }

  /**
   * Show browser notification
   */
  showNotification(sessionData) {
    if (!this.settings.get('showNotifications')) {
      return;
    }

    const isWorkComplete = sessionData.type === 'work';
    const message = isWorkComplete
      ? this.settings.get('workCompleteMessage')
      : this.settings.get('breakCompleteMessage');

    // Show in-app notification
    const notificationType = isWorkComplete ? 'success' : 'info';
    notificationManager.show(message, notificationType, 5000);

    // Show browser notification if enabled
    if (this.settings.get('browserNotifications') && 'Notification' in window && Notification.permission === 'granted') {
      const title = this.settings.get('notificationTitle');
      new Notification(title, {
        body: message,
        icon: '/vite.svg',
        badge: '/vite.svg'
      });
    }
  }

  /**
   * Play notification sound
   */
  playNotificationSound() {
    const currentSession = this.session.getCurrentSession();
    const soundName = currentSession.type === 'work' ? 'workComplete' : 'breakComplete';
    audioManager.play(soundName);
  }

  /**
   * Show break confirmation dialog
   */
  showBreakConfirmation(breakData) {
    const breakType = breakData.breakType === 'longBreak' ? 'Long Break' : 'Short Break';
    const message = `Work session complete! Ready for ${breakType}?`;

    // Show in-app notification with action buttons
    notificationManager.show(
      `${message}\n\nTake your time - any delay will be added as bonus break time!`,
      'success',
      0 // Don't auto-dismiss
    );

    // Also show browser notification if enabled
    if (this.settings.get('browserNotifications') && 'Notification' in window && Notification.permission === 'granted') {
      new Notification('🍅 Pomodoro Timer', {
        body: message,
        icon: '/vite.svg',
        badge: '/vite.svg',
        requireInteraction: true
      });
    }
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window && this.settings.get('browserNotifications')) {
      if (Notification.permission === 'default') {
        await Notification.requestPermission();
      }
    }
  }

  /**
   * Reset entire application state
   */
  resetApp() {
    this.timer.reset();
    this.session.reset();
    this.updateTimerForCurrentSession();
    this.notifyComponents('appReset', this.getState());
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.timer.reset();
    this.components.clear();
    console.log('Pomodoro App destroyed');
  }
}
