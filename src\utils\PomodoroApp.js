/**
 * PomodoroApp Class - Main application state manager
 * Coordinates Timer, Session, and Settings classes
 * Manages overall application state and component communication
 */
import { Timer } from './Timer.js';
import { Session } from './Session.js';
import { Settings } from './Settings.js';
import { audioManager } from './AudioManager.js';
import { notificationManager } from './EventEmitter.js';

export class PomodoroApp {
  constructor() {
    // Initialize core components
    this.settings = new Settings();
    this.session = new Session(this.settings.getTimerSettings());
    this.timer = new Timer();
    
    // Application state
    this.isInitialized = false;
    this.components = new Map(); // Store UI components
    
    // Bind event handlers
    this.setupEventHandlers();
    
    // Initialize timer with current session
    this.updateTimerForCurrentSession();
  }

  /**
   * Initialize the application
   */
  init() {
    if (this.isInitialized) return;
    
    // Apply initial theme
    this.settings.applyTheme();
    
    // Request notification permission if needed
    this.requestNotificationPermission();
    
    this.isInitialized = true;
    console.log('Pomodoro App initialized');
  }

  /**
   * Setup event handlers between components
   */
  setupEventHandlers() {
    // Timer events
    this.timer.on('onTick', (timeRemaining) => {
      this.notifyComponents('timerTick', { timeRemaining, timer: this.timer.getState() });
    });

    this.timer.on('onComplete', () => {
      this.handleTimerComplete();
    });

    this.timer.on('onStart', () => {
      this.notifyComponents('timerStart', this.timer.getState());
    });

    this.timer.on('onPause', () => {
      this.notifyComponents('timerPause', this.timer.getState());
    });

    this.timer.on('onReset', () => {
      this.notifyComponents('timerReset', this.timer.getState());
    });

    // Session events
    this.session.on('onSessionChange', (sessionData) => {
      this.updateTimerForCurrentSession();
      this.notifyComponents('sessionChange', sessionData);
    });

    this.session.on('onSessionComplete', (sessionData) => {
      this.notifyComponents('sessionComplete', sessionData);
      this.showNotification(sessionData);
    });

    this.session.on('onCycleComplete', (cycleData) => {
      this.notifyComponents('cycleComplete', cycleData);
    });

    // Settings events
    this.settings.on('onSettingsChange', (changes) => {
      this.handleSettingsChange(changes);
    });

    this.settings.on('onThemeChange', (theme) => {
      this.settings.applyTheme();
      this.notifyComponents('themeChange', theme);
    });
  }

  /**
   * Handle timer completion
   */
  handleTimerComplete() {
    const currentSession = this.session.getCurrentSession();
    
    // Complete the session
    this.session.completeSession();
    
    // Play notification sound
    if (this.settings.get('playSound')) {
      this.playNotificationSound();
    }
    
    // Auto-start next session if enabled
    if (this.session.shouldAutoStart()) {
      setTimeout(() => {
        this.startTimer();
      }, 1000); // 1 second delay
    }
    
    this.notifyComponents('timerComplete', currentSession);
  }

  /**
   * Handle settings changes
   */
  handleSettingsChange(changes) {
    // Update session settings if timer-related settings changed
    const timerSettings = ['workDuration', 'shortBreakDuration', 'longBreakDuration', 
                          'sessionsUntilLongBreak', 'autoStartBreaks', 'autoStartWork'];
    
    const hasTimerChanges = Object.keys(changes).some(key => timerSettings.includes(key));
    
    if (hasTimerChanges) {
      this.session.updateSettings(this.settings.getTimerSettings());
      this.updateTimerForCurrentSession();
    }
    
    this.notifyComponents('settingsChange', changes);
  }

  /**
   * Update timer duration based on current session
   */
  updateTimerForCurrentSession() {
    const currentSession = this.session.getCurrentSession();
    this.timer.setDuration(currentSession.duration);
  }

  /**
   * Start the timer
   */
  startTimer() {
    this.timer.start();
  }

  /**
   * Pause the timer
   */
  pauseTimer() {
    this.timer.pause();
  }

  /**
   * Reset the timer
   */
  resetTimer() {
    this.timer.reset();
  }

  /**
   * Skip to next session
   */
  skipSession() {
    this.timer.reset();
    this.session.completeSession();
  }

  /**
   * Get current application state
   */
  getState() {
    return {
      timer: this.timer.getState(),
      session: this.session.getCurrentSession(),
      stats: this.session.getStats(),
      settings: this.settings.getAll()
    };
  }

  /**
   * Register a UI component
   */
  registerComponent(name, component) {
    this.components.set(name, component);
  }

  /**
   * Unregister a UI component
   */
  unregisterComponent(name) {
    this.components.delete(name);
  }

  /**
   * Notify all registered components of state changes
   */
  notifyComponents(event, data) {
    this.components.forEach((component) => {
      if (typeof component.handleAppEvent === 'function') {
        component.handleAppEvent(event, data);
      }
    });
  }

  /**
   * Show browser notification
   */
  showNotification(sessionData) {
    if (!this.settings.get('showNotifications')) {
      return;
    }

    const isWorkComplete = sessionData.type === 'work';
    const message = isWorkComplete
      ? this.settings.get('workCompleteMessage')
      : this.settings.get('breakCompleteMessage');

    // Show in-app notification
    const notificationType = isWorkComplete ? 'success' : 'info';
    notificationManager.show(message, notificationType, 5000);

    // Show browser notification if enabled
    if (this.settings.get('browserNotifications') && 'Notification' in window && Notification.permission === 'granted') {
      const title = this.settings.get('notificationTitle');
      new Notification(title, {
        body: message,
        icon: '/vite.svg',
        badge: '/vite.svg'
      });
    }
  }

  /**
   * Play notification sound
   */
  playNotificationSound() {
    const currentSession = this.session.getCurrentSession();
    const soundName = currentSession.type === 'work' ? 'workComplete' : 'breakComplete';
    audioManager.play(soundName);
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window && this.settings.get('browserNotifications')) {
      if (Notification.permission === 'default') {
        await Notification.requestPermission();
      }
    }
  }

  /**
   * Reset entire application state
   */
  resetApp() {
    this.timer.reset();
    this.session.reset();
    this.updateTimerForCurrentSession();
    this.notifyComponents('appReset', this.getState());
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.timer.reset();
    this.components.clear();
    console.log('Pomodoro App destroyed');
  }
}
