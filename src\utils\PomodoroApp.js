/**
 * PomodoroApp Class - Main application state manager
 * Coordinates Timer, Session, and Settings classes
 * Manages overall application state and component communication
 */
import { Timer } from './Timer.js';
import { Session } from './Session.js';
import { Settings } from './Settings.js';
import { NotificationManager } from './NotificationManager.js';
import { EventEmitter } from './EventEmitter.js';
import { musicPlayer } from './MusicPlayer.js';

export class PomodoroApp {
  constructor() {
    // Initialize core components
    this.settings = new Settings();
    this.session = new Session(this.settings.getTimerSettings());
    this.timer = new Timer();
    this.notification = new NotificationManager();
    this.eventEmitter = new EventEmitter();
    this.musicPlayer = musicPlayer;

    // Application state
    this.isInitialized = false;
    this.components = new Map(); // Store UI components
    
    // Bind event handlers
    this.setupEventHandlers();
    
    // Initialize timer with current session
    this.updateTimerForCurrentSession();
  }

  /**
   * Initialize the application
   */
  init() {
    if (this.isInitialized) return;
    
    // Apply initial theme
    this.settings.applyTheme();
    
    // Request notification permission if needed
    this.requestNotificationPermission();
    
    this.isInitialized = true;
    console.log('Pomodoro App initialized');
  }

  /**
   * Setup event handlers between components
   */
  setupEventHandlers() {
    // Timer events
    this.timer.on('onTick', (timeRemaining) => {
      this.notifyComponents('timerTick', { timeRemaining, timer: this.timer.getState() });
    });

    this.timer.on('onComplete', () => {
      this.handleTimerComplete();
    });

    this.timer.on('onStart', () => {
      const sessionData = this.session.getCurrentSession();
      const timerState = this.timer.getState();

      // Detailed logging for session start
      console.log('🚀 SESSION STARTED', {
        type: sessionData.type,
        duration: `${Math.floor(timerState.duration / 60)}:${(timerState.duration % 60).toString().padStart(2, '0')}`,
        sessionNumber: sessionData.sessionNumber,
        totalSessions: sessionData.totalSessions,
        timestamp: new Date().toLocaleTimeString()
      });

      this.notifyComponents('timerStart', timerState);

      // Start music if it's a work session
      if (sessionData.type === 'work') {
        const musicSettings = this.settings.get('enableMusic');
        const selectedTrack = this.settings.get('musicTrack');

        if (musicSettings && selectedTrack) {
          this.musicPlayer.startFocusMusic();
          console.log('🎵 MUSIC STARTED', {
            track: selectedTrack,
            volume: Math.round(this.settings.get('musicVolume') * 100) + '%'
          });
        } else {
          console.log('🎵 Music disabled or no track selected');
        }
      }
    });

    this.timer.on('onPause', () => {
      console.log('⏸️ SESSION PAUSED', {
        timeRemaining: this.formatTime(this.timer.getState().timeRemaining),
        timestamp: new Date().toLocaleTimeString()
      });

      this.notifyComponents('timerPause', this.timer.getState());

      // Pause music when timer is paused
      this.musicPlayer.stopFocusMusic();
      console.log('🎵 Music paused');
    });

    this.timer.on('onReset', () => {
      console.log('🔄 SESSION RESET', {
        sessionType: this.session.getCurrentSession().type,
        timestamp: new Date().toLocaleTimeString()
      });

      this.notifyComponents('timerReset', this.timer.getState());

      // Stop music when timer is reset
      this.musicPlayer.stopFocusMusic();
      console.log('🎵 Music stopped');
    });

    // Session events - DISABLED to prevent conflicts
    // this.session.on('onSessionChange', (sessionData) => {
    //   this.updateTimerForCurrentSession();
    //   this.notifyComponents('sessionChange', sessionData);
    //   // Stop music when session changes
    //   this.musicPlayer.stopFocusMusic();
    // });

    this.session.on('onSessionComplete', (sessionData) => {
      this.notifyComponents('sessionComplete', sessionData);
      this.showNotification(sessionData);
      this.playNotificationSound();
    });

    this.session.on('onBreakPending', (breakData) => {
      const bonusTime = breakData.bonusTime || 0;
      const totalBreakTime = breakData.duration + bonusTime;

      console.log('☕ BREAK PENDING', {
        type: breakData.type,
        baseDuration: this.formatTime(breakData.duration),
        bonusTime: bonusTime > 0 ? this.formatTime(bonusTime) : 'none',
        totalDuration: this.formatTime(totalBreakTime),
        timestamp: new Date().toLocaleTimeString()
      });

      this.notifyComponents('breakPending', breakData);
      this.showBreakConfirmation(breakData);

      // Show notification
      this.notification.showBreakPending(breakData.type, bonusTime);
    });

    this.session.on('onBonusTimeAdded', (bonusData) => {
      this.notifyComponents('bonusTimeAdded', bonusData);
      console.log('⏰ BONUS TIME ADDED', {
        message: bonusData.message,
        bonusTime: this.formatTime(bonusData.bonusTime || 0),
        timestamp: new Date().toLocaleTimeString()
      });
    });

    this.session.on('onCycleComplete', (cycleData) => {
      this.notifyComponents('cycleComplete', cycleData);
    });

    // Settings events
    this.settings.on('onSettingsChange', (changes) => {
      this.handleSettingsChange(changes);
    });

    this.settings.on('onThemeChange', (theme) => {
      this.settings.applyTheme();
      this.notifyComponents('themeChange', theme);
    });
  }

  /**
   * Handle timer completion
   */
  handleTimerComplete() {
    const currentSession = this.session.getCurrentSession();
    
    // Complete the session
    this.session.completeSession();
    
    // Play notification sound
    if (this.settings.get('playSound')) {
      this.playNotificationSound();
    }
    
    // Auto-start next session if enabled
    if (this.session.shouldAutoStart()) {
      setTimeout(() => {
        this.startTimer();
      }, 1000); // 1 second delay
    }
    
    this.notifyComponents('timerComplete', currentSession);
  }

  /**
   * Handle settings changes
   */
  handleSettingsChange(changes) {
    // Update session settings if timer-related settings changed
    const timerSettings = ['workDuration', 'shortBreakDuration', 'longBreakDuration', 
                          'sessionsUntilLongBreak', 'autoStartBreaks', 'autoStartWork'];
    
    const hasTimerChanges = Object.keys(changes).some(key => timerSettings.includes(key));
    
    if (hasTimerChanges) {
      this.session.updateSettings(this.settings.getTimerSettings());
      this.updateTimerForCurrentSession();
    }
    
    this.notifyComponents('settingsChange', changes);
  }

  /**
   * Update timer duration based on current session
   */
  updateTimerForCurrentSession() {
    const currentSession = this.session.getCurrentSession();
    console.log('⏱️ UPDATING TIMER FOR SESSION:', {
      sessionType: currentSession.type,
      duration: currentSession.duration,
      isRunning: this.timer.isRunning,
      currentTimeRemaining: this.timer.timeRemaining
    });

    this.timer.setDuration(currentSession.duration);

    // If timer is not running, reset timeRemaining to new duration
    if (!this.timer.isRunning) {
      this.timer.timeRemaining = currentSession.duration;
      console.log('⏱️ TIMER UPDATED - New timeRemaining:', this.timer.timeRemaining);
    }
  }

  /**
   * Start the timer
   */
  startTimer() {
    this.timer.start();
  }

  /**
   * Pause the timer
   */
  pauseTimer() {
    this.timer.pause();
  }

  /**
   * Reset the timer
   */
  resetTimer() {
    this.timer.reset();
    // Ensure timer is reset to current session duration
    this.updateTimerForCurrentSession();
    this.notifyComponents('timerReset', this.timer.getState());
  }

  /**
   * Skip to next session
   */
  skipSession() {
    // Force complete current timer (this will trigger session completion)
    this.timer.skip();
  }

  /**
   * Confirm and start pending break
   */
  confirmBreak() {
    console.log('🎯 CONFIRM BREAK - Starting break session');

    // Get break info before confirming
    const pendingBreak = this.session.getPendingBreak();
    if (!pendingBreak) {
      console.error('🎯 No pending break found');
      return false;
    }

    // Calculate bonus time
    const now = Date.now();
    const delayTime = Math.floor((now - pendingBreak.completedAt) / 1000);
    const bonusTime = delayTime > 0 ? Math.min(delayTime, 10 * 60) : 0;

    // Get break duration
    const baseDuration = pendingBreak.type === 'longBreak' ?
      this.settings.get('longBreakDuration') : this.settings.get('shortBreakDuration');
    const totalBreakDuration = baseDuration + bonusTime;

    console.log('🎯 BREAK CALCULATION:', {
      breakType: pendingBreak.type,
      baseDuration: baseDuration,
      bonusTime: bonusTime,
      totalDuration: totalBreakDuration
    });

    // Update session state
    this.session.confirmBreak();

    // Force reset timer to exact break duration
    this.timer.forceResetToDuration(totalBreakDuration);

    // Stop music and notify components
    this.musicPlayer.stopFocusMusic();
    this.notifyComponents('sessionChange', this.session.getCurrentSession());
    this.notifyComponents('breakConfirmed', this.getState());

    console.log('🎯 BREAK CONFIRMED - Timer set to:', this.timer.getState());
    return true;
  }

  /**
   * Skip pending break and go to work
   */
  skipBreak() {
    console.log('⏭️ SKIP BREAK - Going to work session');

    // Get work duration
    const workDuration = this.settings.get('workDuration');

    console.log('⏭️ SKIP CALCULATION:', {
      workDuration: workDuration
    });

    // Update session state
    this.session.skipBreak();

    // Force reset timer to work duration
    this.timer.forceResetToDuration(workDuration);

    // Stop music and notify components
    this.musicPlayer.stopFocusMusic();
    this.notifyComponents('sessionChange', this.session.getCurrentSession());
    this.notifyComponents('breakSkipped', this.getState());

    console.log('⏭️ BREAK SKIPPED - Timer set to:', this.timer.getState());
    return true;
  }

  /**
   * Get current application state
   */
  getState() {
    return {
      timer: this.timer.getState(),
      session: this.session.getCurrentSession(),
      stats: this.session.getStats(),
      settings: this.settings.getAll()
    };
  }

  /**
   * Format time in MM:SS format
   */
  formatTime(seconds) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Register a UI component
   */
  registerComponent(name, component) {
    this.components.set(name, component);
  }

  /**
   * Unregister a UI component
   */
  unregisterComponent(name) {
    this.components.delete(name);
  }

  /**
   * Notify all registered components of state changes
   */
  notifyComponents(event, data) {
    this.components.forEach((component) => {
      if (typeof component.handleAppEvent === 'function') {
        component.handleAppEvent(event, data);
      }
    });
  }

  /**
   * Show browser notification
   */
  showNotification(sessionData) {
    if (!this.settings.get('showNotifications')) {
      return;
    }

    console.log('🔔 Showing notification for session:', sessionData.type);

    // Use NotificationManager for browser notifications
    if (this.settings.get('browserNotifications')) {
      this.notification.showSessionComplete(sessionData.type, sessionData.nextType);
    }
  }

  /**
   * Play notification sound
   */
  playNotificationSound() {
    if (!this.settings.get('playSound')) {
      return;
    }

    const volume = this.settings.get('soundVolume') || 0.5;
    this.notification.playNotificationSound(800, 200, volume);
    console.log('🔊 Notification sound played');
  }

  /**
   * Show break confirmation dialog
   */
  showBreakConfirmation(breakData) {
    console.log('💬 BREAK CONFIRMATION DIALOG', {
      breakType: breakData.type,
      duration: this.formatTime(breakData.duration),
      bonusTime: breakData.bonusTime ? this.formatTime(breakData.bonusTime) : 'none',
      timestamp: new Date().toLocaleTimeString()
    });

    // The BreakConfirmation component will handle the UI
    // This method is called to trigger the break confirmation flow
    this.notifyComponents('breakPending', breakData);
  }

  /**
   * Request notification permission
   */
  async requestNotificationPermission() {
    if ('Notification' in window && this.settings.get('browserNotifications')) {
      if (Notification.permission === 'default') {
        await Notification.requestPermission();
      }
    }
  }

  /**
   * Reset entire application state
   */
  resetApp() {
    this.timer.reset();
    this.session.reset();
    this.updateTimerForCurrentSession();
    this.notifyComponents('appReset', this.getState());
  }

  /**
   * Cleanup resources
   */
  destroy() {
    this.timer.reset();
    this.components.clear();
    console.log('Pomodoro App destroyed');
  }
}
