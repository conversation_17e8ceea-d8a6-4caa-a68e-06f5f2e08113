/**
 * Session Class - Manages Pomodoro work/break cycles
 * Handles session counting, automatic transitions, and session types
 */
export class Session {
  constructor(settings = {}) {
    this.settings = {
      workDuration: 25 * 60,      // 25 minutes
      shortBreakDuration: 5 * 60,  // 5 minutes
      longBreakDuration: 15 * 60,  // 15 minutes
      sessionsUntilLongBreak: 4,   // Long break after 4 work sessions
      autoStartBreaks: false,      // Changed: Don't auto start breaks
      autoStartWork: false,        // Auto start work sessions
      ...settings
    };

    this.currentSessionType = 'work'; // 'work', 'shortBreak', 'longBreak'
    this.completedWorkSessions = 0;
    this.totalSessions = 0;
    this.isActive = false;

    // Break confirmation system
    this.pendingBreak = null; // Stores pending break info
    this.workCompleteTime = null; // When work session was completed
    this.bonusBreakTime = 0; // Accumulated bonus break time

    this.callbacks = {
      onSessionChange: [],
      onSessionComplete: [],
      onCycleComplete: [],
      onBreakPending: [],      // New: When break is waiting for confirmation
      onBonusTimeAdded: []     // New: When bonus time is added to break
    };
  }

  /**
   * Get current session configuration
   */
  getCurrentSession() {
    const durations = {
      work: this.settings.workDuration,
      shortBreak: this.settings.shortBreakDuration + this.bonusBreakTime,
      longBreak: this.settings.longBreakDuration + this.bonusBreakTime
    };

    const session = {
      type: this.currentSessionType,
      duration: durations[this.currentSessionType],
      number: this.getSessionNumber(),
      isBreak: this.currentSessionType !== 'work',
      bonusTime: this.bonusBreakTime,
      isPending: this.pendingBreak !== null
    };

    console.log('📋 GET CURRENT SESSION:', {
      type: session.type,
      baseDuration: this.currentSessionType === 'work' ? this.settings.workDuration :
                   this.currentSessionType === 'shortBreak' ? this.settings.shortBreakDuration : this.settings.longBreakDuration,
      bonusTime: this.bonusBreakTime,
      finalDuration: session.duration
    });

    return session;
  }

  /**
   * Get current session number
   */
  getSessionNumber() {
    if (this.currentSessionType === 'work') {
      return this.completedWorkSessions + 1;
    }
    return this.completedWorkSessions;
  }

  /**
   * Complete current session and transition to next
   */
  completeSession() {
    const currentSession = this.getCurrentSession();

    if (this.currentSessionType === 'work') {
      this.completedWorkSessions++;
      this.workCompleteTime = Date.now();

      // Set up pending break instead of auto-transitioning
      const nextSessionType = this.getNextSessionType();
      this.pendingBreak = {
        type: nextSessionType,
        completedAt: this.workCompleteTime
      };

      this.triggerCallbacks('onSessionComplete', currentSession);
      this.triggerCallbacks('onBreakPending', {
        breakType: nextSessionType,
        message: `Work session complete! Ready for ${nextSessionType === 'longBreak' ? 'long break' : 'short break'}?`
      });

    } else {
      // Break sessions complete normally
      this.totalSessions++;
      this.triggerCallbacks('onSessionComplete', currentSession);
      this.setSessionType('work');
      this.bonusBreakTime = 0; // Reset bonus time after break
    }

    // Check if cycle is complete (after long break)
    if (this.currentSessionType === 'work' && this.completedWorkSessions % this.settings.sessionsUntilLongBreak === 0 && this.completedWorkSessions > 0) {
      this.triggerCallbacks('onCycleComplete', {
        completedSessions: this.completedWorkSessions,
        totalSessions: this.totalSessions
      });
    }
  }

  /**
   * Determine next session type based on current state
   */
  getNextSessionType() {
    if (this.currentSessionType === 'work') {
      // After work session, check if it's time for long break
      if (this.completedWorkSessions % this.settings.sessionsUntilLongBreak === 0) {
        return 'longBreak';
      } else {
        return 'shortBreak';
      }
    } else {
      // After any break, return to work
      return 'work';
    }
  }

  /**
   * Set session type manually
   */
  setSessionType(type) {
    if (['work', 'shortBreak', 'longBreak'].includes(type)) {
      this.currentSessionType = type;
      this.triggerCallbacks('onSessionChange', this.getCurrentSession());
    }
  }

  /**
   * Confirm and start pending break
   */
  confirmBreak() {
    if (!this.pendingBreak) return false;

    const now = Date.now();
    const delayTime = Math.floor((now - this.pendingBreak.completedAt) / 1000); // seconds

    console.log('📊 SESSION CONFIRM BREAK:', {
      pendingBreakType: this.pendingBreak.type,
      delayTime: delayTime,
      currentBonusTime: this.bonusBreakTime,
      workDuration: this.settings.workDuration,
      shortBreakDuration: this.settings.shortBreakDuration,
      longBreakDuration: this.settings.longBreakDuration
    });

    // Add delay time as bonus break time (minimum 0, maximum 10 minutes)
    if (delayTime > 0) {
      const bonusTime = Math.min(delayTime, 10 * 60); // Max 10 minutes bonus
      this.bonusBreakTime += bonusTime;

      console.log('🎁 BONUS TIME ADDED:', {
        bonusTime: bonusTime,
        totalBonusTime: this.bonusBreakTime
      });

      this.triggerCallbacks('onBonusTimeAdded', {
        bonusTime: bonusTime,
        totalBonusTime: this.bonusBreakTime,
        delayTime: delayTime,
        message: `+${Math.floor(bonusTime / 60)}:${(bonusTime % 60).toString().padStart(2, '0')} bonus break time!`
      });
    }

    // Start the break session
    this.setSessionType(this.pendingBreak.type);
    this.totalSessions++;
    this.pendingBreak = null;
    this.workCompleteTime = null;

    return true;
  }

  /**
   * Skip pending break and go directly to work
   */
  skipBreak() {
    if (!this.pendingBreak) return false;

    this.pendingBreak = null;
    this.workCompleteTime = null;
    this.bonusBreakTime = 0;
    this.setSessionType('work');

    return true;
  }

  /**
   * Check if there's a pending break
   */
  hasPendingBreak() {
    return this.pendingBreak !== null;
  }

  /**
   * Get pending break info
   */
  getPendingBreak() {
    if (!this.pendingBreak) return null;

    const now = Date.now();
    const delayTime = Math.floor((now - this.pendingBreak.completedAt) / 1000);

    return {
      ...this.pendingBreak,
      delayTime: delayTime,
      potentialBonusTime: Math.min(delayTime, 10 * 60)
    };
  }

  /**
   * Reset session data
   */
  reset() {
    this.currentSessionType = 'work';
    this.completedWorkSessions = 0;
    this.totalSessions = 0;
    this.isActive = false;
    this.pendingBreak = null;
    this.workCompleteTime = null;
    this.bonusBreakTime = 0;
    this.triggerCallbacks('onSessionChange', this.getCurrentSession());
  }

  /**
   * Update settings
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
  }

  /**
   * Get session statistics
   */
  getStats() {
    return {
      completedWorkSessions: this.completedWorkSessions,
      totalSessions: this.totalSessions,
      currentCycle: Math.floor(this.completedWorkSessions / this.settings.sessionsUntilLongBreak) + 1,
      sessionsInCurrentCycle: this.completedWorkSessions % this.settings.sessionsUntilLongBreak,
      sessionsUntilLongBreak: this.settings.sessionsUntilLongBreak - (this.completedWorkSessions % this.settings.sessionsUntilLongBreak)
    };
  }

  /**
   * Check if auto-start is enabled for current session type
   */
  shouldAutoStart() {
    if (this.currentSessionType === 'work') {
      return this.settings.autoStartWork;
    } else {
      return this.settings.autoStartBreaks;
    }
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  /**
   * Trigger callbacks for specific event
   */
  triggerCallbacks(event, data = null) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }
}
