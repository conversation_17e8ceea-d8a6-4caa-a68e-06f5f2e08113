/**
 * Session Class - Manages Pomodoro work/break cycles
 * Handles session counting, automatic transitions, and session types
 */
export class Session {
  constructor(settings = {}) {
    this.settings = {
      workDuration: 25 * 60,      // 25 minutes
      shortBreakDuration: 5 * 60,  // 5 minutes
      longBreakDuration: 15 * 60,  // 15 minutes
      sessionsUntilLongBreak: 4,   // Long break after 4 work sessions
      autoStartBreaks: true,       // Auto start break sessions
      autoStartWork: false,        // Auto start work sessions
      ...settings
    };

    this.currentSessionType = 'work'; // 'work', 'shortBreak', 'longBreak'
    this.completedWorkSessions = 0;
    this.totalSessions = 0;
    this.isActive = false;
    
    this.callbacks = {
      onSessionChange: [],
      onSessionComplete: [],
      onCycleComplete: []
    };
  }

  /**
   * Get current session configuration
   */
  getCurrentSession() {
    const durations = {
      work: this.settings.workDuration,
      shortBreak: this.settings.shortBreakDuration,
      longBreak: this.settings.longBreakDuration
    };

    return {
      type: this.currentSessionType,
      duration: durations[this.currentSessionType],
      number: this.getSessionNumber(),
      isBreak: this.currentSessionType !== 'work'
    };
  }

  /**
   * Get current session number
   */
  getSessionNumber() {
    if (this.currentSessionType === 'work') {
      return this.completedWorkSessions + 1;
    }
    return this.completedWorkSessions;
  }

  /**
   * Complete current session and transition to next
   */
  completeSession() {
    const currentSession = this.getCurrentSession();
    
    if (this.currentSessionType === 'work') {
      this.completedWorkSessions++;
    }
    
    this.totalSessions++;
    this.triggerCallbacks('onSessionComplete', currentSession);

    // Determine next session type
    const nextSessionType = this.getNextSessionType();
    this.setSessionType(nextSessionType);

    // Check if cycle is complete (after long break)
    if (this.currentSessionType === 'work' && this.completedWorkSessions % this.settings.sessionsUntilLongBreak === 0 && this.completedWorkSessions > 0) {
      this.triggerCallbacks('onCycleComplete', {
        completedSessions: this.completedWorkSessions,
        totalSessions: this.totalSessions
      });
    }
  }

  /**
   * Determine next session type based on current state
   */
  getNextSessionType() {
    if (this.currentSessionType === 'work') {
      // After work session, check if it's time for long break
      if (this.completedWorkSessions % this.settings.sessionsUntilLongBreak === 0) {
        return 'longBreak';
      } else {
        return 'shortBreak';
      }
    } else {
      // After any break, return to work
      return 'work';
    }
  }

  /**
   * Set session type manually
   */
  setSessionType(type) {
    if (['work', 'shortBreak', 'longBreak'].includes(type)) {
      this.currentSessionType = type;
      this.triggerCallbacks('onSessionChange', this.getCurrentSession());
    }
  }

  /**
   * Reset session data
   */
  reset() {
    this.currentSessionType = 'work';
    this.completedWorkSessions = 0;
    this.totalSessions = 0;
    this.isActive = false;
    this.triggerCallbacks('onSessionChange', this.getCurrentSession());
  }

  /**
   * Update settings
   */
  updateSettings(newSettings) {
    this.settings = { ...this.settings, ...newSettings };
  }

  /**
   * Get session statistics
   */
  getStats() {
    return {
      completedWorkSessions: this.completedWorkSessions,
      totalSessions: this.totalSessions,
      currentCycle: Math.floor(this.completedWorkSessions / this.settings.sessionsUntilLongBreak) + 1,
      sessionsInCurrentCycle: this.completedWorkSessions % this.settings.sessionsUntilLongBreak,
      sessionsUntilLongBreak: this.settings.sessionsUntilLongBreak - (this.completedWorkSessions % this.settings.sessionsUntilLongBreak)
    };
  }

  /**
   * Check if auto-start is enabled for current session type
   */
  shouldAutoStart() {
    if (this.currentSessionType === 'work') {
      return this.settings.autoStartWork;
    } else {
      return this.settings.autoStartBreaks;
    }
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  /**
   * Trigger callbacks for specific event
   */
  triggerCallbacks(event, data = null) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }
}
