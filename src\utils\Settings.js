/**
 * Settings Class - Manages application settings and localStorage persistence
 * Handles user preferences, themes, and configuration options
 */
export class Settings {
  constructor() {
    this.defaultSettings = {
      // Timer durations (in seconds)
      workDuration: 25 * 60,
      shortBreakDuration: 5 * 60,
      longBreakDuration: 15 * 60,
      
      // Session settings
      sessionsUntilLongBreak: 4,
      autoStartBreaks: true,
      autoStartWork: false,
      
      // UI settings
      theme: 'light', // 'light', 'dark', 'auto'
      showNotifications: true,
      playSound: true,
      soundVolume: 0.5,
      
      // Display settings
      showProgressBar: true,
      showSessionCount: true,
      minimalistMode: false,
      
      // Notification settings
      notificationTitle: 'Pomodoro Timer',
      workCompleteMessage: 'Work session complete! Time for a break.',
      breakCompleteMessage: 'Break time is over! Ready to work?',
      
      // Advanced settings
      tickSound: false,
      browserNotifications: true,
      confirmReset: true
    };

    this.currentSettings = { ...this.defaultSettings };
    this.storageKey = 'pomodoro-settings';
    
    this.callbacks = {
      onSettingsChange: [],
      onThemeChange: []
    };

    // Load settings from localStorage on initialization
    this.loadSettings();
  }

  /**
   * Get all current settings
   */
  getAll() {
    return { ...this.currentSettings };
  }

  /**
   * Get specific setting value
   */
  get(key) {
    return this.currentSettings[key];
  }

  /**
   * Set specific setting value
   */
  set(key, value) {
    if (key in this.defaultSettings) {
      const oldValue = this.currentSettings[key];
      this.currentSettings[key] = value;
      
      // Trigger specific callbacks
      if (key === 'theme' && oldValue !== value) {
        this.triggerCallbacks('onThemeChange', value);
      }
      
      this.triggerCallbacks('onSettingsChange', { key, value, oldValue });
      this.saveSettings();
    }
  }

  /**
   * Update multiple settings at once
   */
  update(settings) {
    const changes = {};
    
    Object.keys(settings).forEach(key => {
      if (key in this.defaultSettings) {
        const oldValue = this.currentSettings[key];
        this.currentSettings[key] = settings[key];
        changes[key] = { oldValue, newValue: settings[key] };
      }
    });

    // Check for theme change
    if ('theme' in changes) {
      this.triggerCallbacks('onThemeChange', changes.theme.newValue);
    }

    this.triggerCallbacks('onSettingsChange', changes);
    this.saveSettings();
  }

  /**
   * Reset settings to default values
   */
  reset() {
    this.currentSettings = { ...this.defaultSettings };
    this.saveSettings();
    this.triggerCallbacks('onSettingsChange', this.currentSettings);
  }

  /**
   * Save settings to localStorage
   */
  saveSettings() {
    try {
      localStorage.setItem(this.storageKey, JSON.stringify(this.currentSettings));
    } catch (error) {
      console.warn('Failed to save settings to localStorage:', error);
    }
  }

  /**
   * Load settings from localStorage
   */
  loadSettings() {
    try {
      const saved = localStorage.getItem(this.storageKey);
      if (saved) {
        const parsedSettings = JSON.parse(saved);
        
        // Merge with defaults to ensure all keys exist
        this.currentSettings = {
          ...this.defaultSettings,
          ...parsedSettings
        };
        
        // Apply theme immediately
        this.applyTheme();
      }
    } catch (error) {
      console.warn('Failed to load settings from localStorage:', error);
      this.currentSettings = { ...this.defaultSettings };
    }
  }

  /**
   * Apply theme to document
   */
  applyTheme() {
    const theme = this.currentSettings.theme;
    const root = document.documentElement;
    
    if (theme === 'auto') {
      // Use system preference
      const prefersDark = window.matchMedia('(prefers-color-scheme: dark)').matches;
      root.setAttribute('data-theme', prefersDark ? 'dark' : 'light');
    } else {
      root.setAttribute('data-theme', theme);
    }
  }

  /**
   * Get settings for specific category
   */
  getTimerSettings() {
    return {
      workDuration: this.currentSettings.workDuration,
      shortBreakDuration: this.currentSettings.shortBreakDuration,
      longBreakDuration: this.currentSettings.longBreakDuration,
      sessionsUntilLongBreak: this.currentSettings.sessionsUntilLongBreak,
      autoStartBreaks: this.currentSettings.autoStartBreaks,
      autoStartWork: this.currentSettings.autoStartWork
    };
  }

  /**
   * Get UI settings
   */
  getUISettings() {
    return {
      theme: this.currentSettings.theme,
      showProgressBar: this.currentSettings.showProgressBar,
      showSessionCount: this.currentSettings.showSessionCount,
      minimalistMode: this.currentSettings.minimalistMode
    };
  }

  /**
   * Get notification settings
   */
  getNotificationSettings() {
    return {
      showNotifications: this.currentSettings.showNotifications,
      playSound: this.currentSettings.playSound,
      soundVolume: this.currentSettings.soundVolume,
      browserNotifications: this.currentSettings.browserNotifications,
      tickSound: this.currentSettings.tickSound
    };
  }

  /**
   * Export settings as JSON
   */
  export() {
    return JSON.stringify(this.currentSettings, null, 2);
  }

  /**
   * Import settings from JSON
   */
  import(jsonString) {
    try {
      const importedSettings = JSON.parse(jsonString);
      this.update(importedSettings);
      return true;
    } catch (error) {
      console.error('Failed to import settings:', error);
      return false;
    }
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  /**
   * Trigger callbacks for specific event
   */
  triggerCallbacks(event, data = null) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }
}
