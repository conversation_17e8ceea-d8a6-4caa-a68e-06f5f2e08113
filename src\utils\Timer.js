/**
 * Timer Class - Core timer functionality for Pomodoro application
 * Handles countdown logic, state management, and timer events
 */
export class Timer {
  constructor(duration = 25 * 60) { // Default 25 minutes in seconds
    this.duration = duration;
    this.timeRemaining = duration;
    this.isRunning = false;
    this.isPaused = false;
    this.intervalId = null;
    this.callbacks = {
      onTick: [],
      onComplete: [],
      onStart: [],
      onPause: [],
      onReset: []
    };
  }

  /**
   * Start the timer
   */
  start() {
    if (this.isRunning) return;
    
    this.isRunning = true;
    this.isPaused = false;
    
    this.intervalId = setInterval(() => {
      this.timeRemaining--;
      this.triggerCallbacks('onTick', this.timeRemaining);
      
      if (this.timeRemaining <= 0) {
        this.complete();
      }
    }, 1000);
    
    this.triggerCallbacks('onStart');
  }

  /**
   * Pause the timer
   */
  pause() {
    if (!this.isRunning) return;
    
    this.isRunning = false;
    this.isPaused = true;
    clearInterval(this.intervalId);
    
    this.triggerCallbacks('onPause');
  }

  /**
   * Reset the timer to initial duration
   */
  reset() {
    this.isRunning = false;
    this.isPaused = false;
    clearInterval(this.intervalId);
    this.timeRemaining = this.duration;
    
    this.triggerCallbacks('onReset');
  }

  /**
   * Complete the timer (called when time reaches 0)
   */
  complete() {
    this.isRunning = false;
    this.isPaused = false;
    clearInterval(this.intervalId);
    this.timeRemaining = 0;

    this.triggerCallbacks('onComplete');
  }

  /**
   * Skip the timer (force complete without waiting)
   */
  skip() {
    this.isRunning = false;
    this.isPaused = false;
    clearInterval(this.intervalId);
    this.timeRemaining = 0;

    this.triggerCallbacks('onComplete');
  }

  /**
   * Set new duration for the timer
   */
  setDuration(duration) {
    this.duration = duration;
    // Always update timeRemaining when timer is not running or paused
    if (!this.isRunning || this.isPaused) {
      this.timeRemaining = duration;
    }
  }

  /**
   * Get current state of the timer
   */
  getState() {
    return {
      duration: this.duration,
      timeRemaining: this.timeRemaining,
      isRunning: this.isRunning,
      isPaused: this.isPaused,
      progress: ((this.duration - this.timeRemaining) / this.duration) * 100
    };
  }

  /**
   * Format time in MM:SS format
   */
  formatTime(seconds = this.timeRemaining) {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  }

  /**
   * Add event listener
   */
  on(event, callback) {
    if (this.callbacks[event]) {
      this.callbacks[event].push(callback);
    }
  }

  /**
   * Remove event listener
   */
  off(event, callback) {
    if (this.callbacks[event]) {
      const index = this.callbacks[event].indexOf(callback);
      if (index > -1) {
        this.callbacks[event].splice(index, 1);
      }
    }
  }

  /**
   * Trigger callbacks for specific event
   */
  triggerCallbacks(event, data = null) {
    if (this.callbacks[event]) {
      this.callbacks[event].forEach(callback => callback(data));
    }
  }
}
